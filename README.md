# AI Book Creator

## Description

The AI Book Creator is a web application designed to assist users in the initial stages of book writing and conceptualization. It leverages (simulated) AI to help generate book content, including titles and chapter text. Users can manage book structure, incorporate images (via local upload or simulated AI generation), customize export formats (PDF, EPUB), and preview their work in an interactive reader with page-turning effects and Text-to-Speech (TTS) functionality. The application also features theme customization for user interface preference.

## Features

*   **AI Content Generation (Simulated):** Generates initial book ideas, titles, and chapter content based on user prompts (genre, audience, keywords, style).
*   **Book Structure Management:** Allows users to define the number of chapters and provide custom chapter titles.
*   **Image Integration:**
    *   **Local Image Upload:** Upload cover images and images for chapters.
    *   **AI Image Generation (Simulated):** Generate placeholder images for covers and chapters based on style and prompts.
*   **Interactive Book Preview:**
    *   **Page-Turning Effect:** Uses Turn.js to simulate a real book reading experience.
    *   **Text-to-Speech (TTS):** Reads page content aloud using the browser's Web Speech API, with options for voice and rate control.
    *   **Customizable Background:** Allows users to select different background patterns for the previewer.
*   **Export Options:**
    *   **PDF Export:** Generates a PDF version of the book using WeasyPrint.
    *   **EPUB Export:** Generates an EPUB version of the book using EbookLib.
    *   **Export Customization:** Users can customize PDF page size, margins, and global font settings (family, size) for both PDF and EPUB exports.
*   **Design Customization (Theming):** Offers Light (default), Dark, and Sepia themes for the user interface, with preferences saved locally.
*   **KDP Export Guidance:** Provides advice on using the exported EPUB and PDF files for publishing on Amazon KDP.
*   **Responsive UI:** Basic responsiveness for usability on various screen sizes.

## Project Structure

*   `run.py`: The main Flask application runner.
*   `requirements.txt`: Lists all Python dependencies.
*   `app/`: Main application directory.
    *   `__init__.py`: Initializes the Flask application, configures settings (like upload folder, secret key).
    *   `routes.py`: Defines all Flask routes, view functions, and core backend logic (including simulations, export generation).
    *   `static/`: Contains static assets.
        *   `style.css`: Main stylesheet with CSS variables and theme definitions.
        *   `script.js`: Global JavaScript for theme switching, dynamic form interactions, etc.
        *   `lib/`: For third-party libraries (e.g., `turnjs/`).
        *   `uploads/`: Default directory for user-uploaded images (gitignored, except for `.keep` file).
        *   `ai_placeholders/`: Contains placeholder images for simulated AI image generation.
        *   `background_patterns/`: Contains patterns for the book preview background.
        *   `icons/`: SVG icons for UI elements (e.g., TTS controls).
        *   `pdf_style.css`: Base CSS specifically for PDF export styling.
        *   `epub_style.css`: Base CSS specifically for EPUB export styling.
    *   `templates/`: Contains HTML templates.
        *   `index.html`: Main page for initiating book creation.
        *   `edit_book.html`: Page for editing book details, managing images, customizing exports, and accessing KDP guidance.
        *   `book_preview.html`: Interactive book preview with Turn.js and TTS.
        *   `pdf_template.html`: HTML structure used by WeasyPrint for PDF generation.
        *   `_header.html`: Reusable header snippet included in main pages.

## Setup Instructions

1.  **Python Version:** Python 3.8 or newer is recommended.

2.  **Create and Activate Virtual Environment:**
    ```bash
    python -m venv venv
    # On Windows
    # venv\Scripts\activate
    # On macOS/Linux
    # source venv/bin/activate
    ```

3.  **Install System Dependencies (for WeasyPrint on Debian/Ubuntu):**
    WeasyPrint is used for PDF generation and requires certain system libraries. If you are on a Debian-based system (like Ubuntu), install them using:
    ```bash
    sudo apt-get update
    sudo apt-get install -y libpango-1.0-0 libcairo2 libgdk-pixbuf2.0-0
    ```
    For other operating systems, please refer to the WeasyPrint documentation for installation prerequisites.

4.  **Install Python Dependencies:**
    With your virtual environment activated, install the required Python packages:
    ```bash
    pip install -r requirements.txt
    ```

## Running the Application

1.  **Run the Flask Development Server:**
    Ensure your virtual environment is activated and you are in the project root directory.
    ```bash
    python run.py
    ```

2.  **Access the Application:**
    Open your web browser and navigate to:
    [http://127.0.0.1:5000/](http://127.0.0.1:5000/)

## Simulated Services

Please note that the core AI functionalities in this application are **simulated**:

*   **LLM Content Generation:** The book content (titles, chapter text) is generated by placeholder functions in `app/routes.py`. Look for `simulate_llm_api_call(prompt_text, form_data)` to integrate a real Large Language Model API.
*   **AI Image Generation:** Image generation based on prompts is also simulated. The function `simulate_ai_image_generation(prompt, style)` in `app/routes.py` currently returns paths to pre-existing placeholder images. This is where you would integrate an actual AI image generation API (e.g., DALL-E, Stable Diffusion).

## Future Enhancements (Optional)

*   Integrate actual LLM APIs (e.g., OpenAI GPT, Anthropic Claude) for content generation.
*   Integrate real AI Image Generation APIs.
*   Implement user accounts and database storage for saving projects.
*   Add a "Save Book" feature to persist edits made on the `/edit-book` page.
*   Allow for reordering/adding/deleting chapters.
*   More advanced export customization options (e.g., specific PDF trim sizes beyond presets, more font choices, EPUB metadata editing).
*   Implement dynamic pagination of long chapter content in the Turn.js preview.
*   Enhance UI/UX with more interactive elements and potentially a JavaScript frontend framework.
*   Add more robust error handling and input validation.

---
This README provides a comprehensive guide to understanding, setting up, and running the AI Book Creator application.
