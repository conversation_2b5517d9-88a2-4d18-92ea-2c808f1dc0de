:root {
    /* Light Theme (Default) */
    --font-family-sans: 'Arial', sans-serif;
    --font-family-serif: 'Georgia', serif;
    --font-family-mono: 'Courier New', monospace;

    --bg-color: #f4f4f4;
    --text-color: #333;
    --container-bg-color: #fff;
    --container-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    --border-color: #ddd;
    --border-color-light: #eee;
    --border-color-dark: #ccc;
    
    --header-text-color: #2c3e50;
    --label-text-color: #555;
    --input-text-color: #333; /* Text color inside inputs */
    --input-bg-color: #fff; /* Background of inputs */
    --input-focus-border-color: #3498db;

    --button-primary-bg: #3498db;
    --button-primary-text: white;
    --button-primary-hover-bg: #2980b9;

    --button-secondary-bg: #6c757d;
    --button-secondary-text: white;
    --button-secondary-hover-bg: #5a6268;
    
    --button-success-bg: #28a745;
    --button-success-text: white;
    --button-success-hover-bg: #218838;

    --button-info-bg: #17a2b8;
    --button-info-text: white;
    --button-info-hover-bg: #138496;

    --button-warning-bg: #ffc107;
    --button-warning-text: #212529;
    --button-warning-hover-bg: #e0a800;

    --button-ai-generate-bg: #5cb85c;
    --button-ai-generate-text: white;
    --button-ai-generate-hover-bg: #4cae4c;

    --alert-success-bg: #d4edda;
    --alert-success-text: #155724;
    --alert-success-border: #c3e6cb;

    --alert-error-bg: #f8d7da;
    --alert-error-text: #721c24;
    --alert-error-border: #f5c6cb;

    --alert-info-bg: #d1ecf1;
    --alert-info-text: #0c5460;
    --alert-info-border: #bee5eb;
    
    --alert-warning-bg-alt: #fff3cd; /* Different from button warning */
    --alert-warning-text-alt: #856404;
    --alert-warning-border-alt: #ffeeba;

    --sub-section-bg: #f9f9f9; /* For chapter-editor, image-management, export-settings */
    --sub-section-border: #e0e0e0;

    --debug-bg: #f8f9fa;
    --debug-border: #dee2e6;
    --debug-text-color: #495057;
    --debug-pre-bg: #e9ecef;
    --debug-pre-text: #212529;

    --link-color: var(--button-primary-bg);
    --link-hover-color: var(--button-primary-hover-bg);
    
    --preview-body-bg: #ccc; /* For book_preview.html body */
    --preview-controls-bg: rgba(255, 255, 255, 0.85);
    --preview-page-bg: white;
    --preview-page-border: #999;
    --preview-page-text-color: #333;
    --preview-page-header-color: #333;
    --preview-nav-button-bg: #4CAF50;
    --preview-nav-button-text: white;
    --preview-nav-button-hover-bg: #45a049;
}

body {
    font-family: var(--font-family-sans);
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    /* Removed padding: 20px; and display:flex from body to allow full-width header */
    /* and prevent double padding when containers have their own. */
    /* min-height: 100vh; */ /* Can cause issues with sticky footer if content is short */
    transition: background-color 0.3s, color 0.3s;
}

.page-wrapper { /* New wrapper to re-apply centering for pages that need it */
    display: flex;
    flex-direction: column; /* Stack header and container */
    justify-content: flex-start; /* Align items to the start */
    align-items: center;
    min-height: 100vh;
    padding: 0 20px 20px 20px; /* Add padding here, leaving top for sticky header */
    box-sizing: border-box;
}


.container {
    background-color: var(--container-bg-color);
    padding: 30px;
    border-radius: 8px;
    box-shadow: var(--container-shadow);
    width: 100%;
    max-width: 700px;
}

h1 {
    text-align: center;
    color: var(--header-text-color);
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--label-text-color);
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
    transition: border-color 0.3s;
    background-color: var(--input-bg-color);
    color: var(--input-text-color);
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--input-focus-border-color);
    outline: none;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group input[type="submit"],
.form-group button[type="button"], /* General buttons not covered by specific classes like btn-info */
.btn-primary { /* Save Export Settings button */
    background-color: var(--button-primary-bg);
    color: var(--button-primary-text);
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
    display: inline-block;
    text-decoration: none; /* For <a> tags styled as buttons */
    text-align: center;
    vertical-align: middle;
}

/* Base .btn class */
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.5rem 1rem; /* Default padding */
    font-size: 1rem; /* Default font size */
    line-height: 1.5;
    border-radius: 0.25rem; /* Default border radius */
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    text-decoration: none; /* For <a> tags */
}

.btn-primary {
    color: var(--button-primary-text);
    background-color: var(--button-primary-bg);
    border-color: var(--button-primary-bg);
}
.btn-primary:hover {
    color: var(--button-primary-text);
    background-color: var(--button-primary-hover-bg);
    border-color: var(--button-primary-hover-bg);
}

.btn-secondary {
    color: var(--button-secondary-text);
    background-color: var(--button-secondary-bg);
    border-color: var(--button-secondary-bg);
}
.btn-secondary:hover {
    color: var(--button-secondary-text);
    background-color: var(--button-secondary-hover-bg);
    border-color: var(--button-secondary-hover-bg);
}

.btn-success {
    color: var(--button-success-text);
    background-color: var(--button-success-bg);
    border-color: var(--button-success-bg);
}
.btn-success:hover {
    color: var(--button-success-text);
    background-color: var(--button-success-hover-bg);
    border-color: var(--button-success-hover-bg);
}

.btn-info {
    color: var(--button-info-text);
    background-color: var(--button-info-bg);
    border-color: var(--button-info-bg);
}
.btn-info:hover {
    color: var(--button-info-text);
    background-color: var(--button-info-hover-bg);
    border-color: var(--button-info-hover-bg);
}

.btn-warning {
    color: var(--button-warning-text);
    background-color: var(--button-warning-bg);
    border-color: var(--button-warning-bg);
}
.btn-warning:hover {
    color: var(--button-warning-text);
    background-color: var(--button-warning-hover-bg);
    border-color: var(--button-warning-hover-bg);
}

.btn-ai-generate {
    color: var(--button-ai-generate-text);
    background-color: var(--button-ai-generate-bg);
    border-color: var(--button-ai-generate-bg);
}
.btn-ai-generate:hover {
    color: var(--button-ai-generate-text);
    background-color: var(--button-ai-generate-hover-bg);
    border-color: var(--button-ai-generate-hover-bg);
}

.btn-large {
    padding: 0.75rem 1.5rem;
    font-size: 1.25rem;
}

.btn-nav { /* For book preview Prev/Next */
    background-color: var(--preview-nav-button-bg);
    color: var(--preview-nav-button-text);
    padding: 8px 15px;
    font-size: 0.9em;
}
.btn-nav:hover {
    background-color: var(--preview-nav-button-hover-bg);
    color: var(--preview-nav-button-text);
}

.btn-tts { /* For TTS controls */
    background-color: var(--button-warning-bg); /* Using warning as a base, can be different */
    color: var(--button-warning-text);
    padding: 8px 12px;
    font-size: 0.9em;
}
.btn-tts:hover {
    background-color: var(--button-warning-hover-bg);
    color: var(--button-warning-text);
}
.btn-tts:disabled {
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-text);
    opacity: 0.65;
    cursor: not-allowed;
}


/* Overriding generic .form-group button for more specific .btn classes */
.form-group input[type="submit"],
.form-group button[type="button"] {
    /* Styles are now primarily from .btn and its variants */
}
.form-group input[type="submit"]:hover, /* Keep this if inputs are not getting .btn:hover */
.form-group button[type="button"]:hover {
     /* Covered by .btn:hover and its variants */
}


fieldset.form-group { /* Used in main form */
    border: 1px solid var(--border-color);
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

fieldset.form-group legend { /* Used in main form */
    font-weight: bold;
    color: var(--text-color);
    padding: 0 10px;
    margin-left: 5px;
    font-size: 1.1em;
}

/* ... (other existing rules updated to use vars) ... */

.editor-container { max-width: 900px; }
.editor-header { border-bottom: 1px solid var(--border-color-light); }
.editor-header h1 { color: var(--header-text-color); }

.back-link { background-color: var(--button-secondary-bg); color: var(--button-secondary-text); }
.back-link:hover { background-color: var(--button-secondary-hover-bg); }

.chapter-editor { background-color: var(--sub-section-bg); border: 1px solid var(--sub-section-border); }
.chapter-editor h2 { color: var(--text-color); }

.editable-title.chapter-title-input,
.editable-content.chapter-content-textarea {
    border: 1px solid var(--border-color-dark);
    background-color: var(--input-bg-color);
    color: var(--input-text-color);
}
.editable-content.chapter-content-textarea { font-family: var(--font-family-serif); }

.editor-actions button { /* This is the "Save Changes" button specifically */
    background-color: var(--button-success-bg);
    color: var(--button-success-text);
}
.editor-actions button:hover { background-color: var(--button-success-hover-bg); }

.btn-info { background-color: var(--button-info-bg); color: var(--button-info-text); }
.btn-info:hover { background-color: var(--button-info-hover-bg); }
.btn-success { background-color: var(--button-success-bg); color: var(--button-success-text); } /* For Download PDF */
.btn-success:hover { background-color: var(--button-success-hover-bg); }
.btn-warning { background-color: var(--button-warning-bg); color: var(--button-warning-text); } /* For Download EPUB */
.btn-warning:hover { background-color: var(--button-warning-hover-bg); border-color: var(--button-warning-hover-bg); }


.debug-info { background-color: var(--debug-bg); border: 1px solid var(--debug-border); }
.debug-info h3 { color: var(--debug-text-color); }
.debug-info pre { background-color: var(--debug-pre-bg); color: var(--debug-pre-text); }

.flash-messages .alert-success { background-color: var(--alert-success-bg); color: var(--alert-success-text); border-color: var(--alert-success-border); }
.flash-messages .alert-error { background-color: var(--alert-error-bg); color: var(--alert-error-text); border-color: var(--alert-error-border); }
.flash-messages .alert-info { background-color: var(--alert-info-bg); color: var(--alert-info-text); border-color: var(--alert-info-border); }
.flash-messages .alert-warning { background-color: var(--alert-warning-bg-alt); color: var(--alert-warning-text-alt); border-color: var(--alert-warning-border-alt); }

.image-management-area { background-color: var(--sub-section-bg); border: 1px solid var(--sub-section-border); }
.image-management-area h3, .image-management-area h4 { color: var(--text-color); border-bottom: 1px solid var(--border-color-light); }
.upload-form, .ai-generation-form { background-color: var(--container-bg-color); border: 1px solid var(--border-color-light); }
.ai-generation-form input[type="text"].form-control { border: 1px solid var(--border-color-dark); background-color: var(--input-bg-color); color: var(--input-text-color); }

.btn-ai-generate { background-color: var(--button-ai-generate-bg); color: var(--button-ai-generate-text); }
.btn-ai-generate:hover { background-color: var(--button-ai-generate-hover-bg); }

.export-settings-section { background-color: var(--sub-section-bg); border: 1px solid var(--sub-section-border); }
.export-settings-section h4 { color: var(--header-text-color); border-bottom: 1px solid var(--border-color-dark); }
.export-settings-section fieldset { border: 1px solid var(--border-color-dark); }
.export-settings-section legend { color: var(--text-color); }
.export-settings-section .btn-primary { /* Save Export Settings button */
    background-color: var(--button-primary-bg); 
    color: var(--button-primary-text);
}
.export-settings-section .btn-primary:hover { background-color: var(--button-primary-hover-bg); }


.kdp-guidance { background-color: var(--debug-bg); border: 1px solid var(--debug-border); }
.kdp-guidance h4 { color: var(--header-text-color); border-bottom: 1px solid var(--border-color-dark); }
.kdp-guidance h5 { color: var(--debug-text-color); }

/* Theme Definitions */
body.theme-dark {
    --bg-color: #22272e; /* Dark gray-blue */
    --text-color: #c9d1d9; /* Light gray */
    --container-bg-color: #2d333b; /* Slightly lighter dark gray-blue */
    --container-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
    --border-color: #444c56; /* Mid-gray for borders */
    --border-color-light: #373e47;
    --border-color-dark: #586069;

    --header-text-color: #58a6ff; /* Light blue for headers */
    --label-text-color: #8b949e; /* Lighter gray for labels */
    --input-text-color: #c9d1d9;
    --input-bg-color: #22272e;
    --input-focus-border-color: #58a6ff; /* Light blue focus */

    --button-primary-bg: #3081f7; /* Brighter blue for primary buttons */
    --button-primary-hover-bg: #1f6feb;
    --button-secondary-bg: #484f58; /* Darker gray for secondary buttons */
    --button-secondary-hover-bg: #30363d;
    --button-success-bg: #238636;
    --button-success-hover-bg: #1a6f2c;
    --button-info-bg: #31b0d5;
    --button-info-hover-bg: #208ea9;
    --button-warning-bg: #e7b00a;
    --button-warning-text: #22272e; /* Dark text for yellow button */
    --button-warning-hover-bg: #c99700;
    --button-ai-generate-bg: #2ea043;
    --button-ai-generate-hover-bg: #238636;

    --alert-success-bg: #2ea04326;
    --alert-success-text: #7ee787;
    --alert-success-border: #7ee7874d;
    --alert-error-bg: #f8514926;
    --alert-error-text: #ff7b72;
    --alert-error-border: #ff7b724d;
    --alert-info-bg: #58a6ff26;
    --alert-info-text: #79c0ff;
    --alert-info-border: #79c0ff4d;
    --alert-warning-bg-alt: #e7b00a26;
    --alert-warning-text-alt: #f0b917;
    --alert-warning-border-alt: #f0b9174d;

    --sub-section-bg: #2d333b;
    --sub-section-border: #444c56;

    --debug-bg: #2d333b;
    --debug-border: #444c56;
    --debug-text-color: #8b949e;
    --debug-pre-bg: #22272e;
    --debug-pre-text: #c9d1d9;

    --link-color: var(--button-primary-bg);
    --link-hover-color: var(--button-primary-hover-bg);
    
    --preview-body-bg: #1c2128; /* Darker background for preview page */
    --preview-controls-bg: rgba(45, 51, 59, 0.85); /* Darker controls bar */
    --preview-page-bg: #22272e; /* Dark pages */
    --preview-page-border: #444c56;
    --preview-page-text-color: #c9d1d9;
    --preview-page-header-color: #58a6ff;
    --preview-nav-button-bg: #238636;
    --preview-nav-button-hover-bg: #1a6f2c;
}

body.theme-sepia {
    --bg-color: #f5e8d0; /* Sepia background */
    --text-color: #5b4636; /* Dark brown text */
    --container-bg-color: #efe0c6; /* Lighter sepia for containers */
    --container-shadow: 0 0 15px rgba(80, 60, 40, 0.2);
    --border-color: #d3c0a5;
    --border-color-light: #e0d0b8;
    --border-color-dark: #c0a080;

    --header-text-color: #704214; /* Darker brown for headers */
    --label-text-color: #6f4e28;
    --input-text-color: #5b4636;
    --input-bg-color: #fdf3e3;
    --input-focus-border-color: #8c5e35; /* Brown focus */

    --button-primary-bg: #8c5e35; /* Brown for primary buttons */
    --button-primary-text: #f5e8d0; /* Light text on brown buttons */
    --button-primary-hover-bg: #704214;
    --button-secondary-bg: #a57d50;
    --button-secondary-hover-bg: #8c6c40;
    --button-success-bg: #5a7d48; /* Muted green */
    --button-success-hover-bg: #486838;
    --button-info-bg: #50808c; /* Muted blue/teal */
    --button-info-hover-bg: #406870;
    --button-warning-bg: #c8983b; /* Muted orange/yellow */
    --button-warning-text: #f5e8d0;
    --button-warning-hover-bg: #a8782b;
    --button-ai-generate-bg: #6a8a58; /* Muted green */
    --button-ai-generate-hover-bg: #587848;

    --alert-success-bg: #d8e8d0;
    --alert-success-text: #486838;
    --alert-success-border: #c0d0b8;
    --alert-error-bg: #f8d7da; /* Keep similar for contrast */
    --alert-error-text: #721c24;
    --alert-error-border: #f5c6cb;
    --alert-info-bg: #d1e0e8;
    --alert-info-text: #406870;
    --alert-info-border: #b8c8d0;
    --alert-warning-bg-alt: #fff3cd; /* Keep similar for contrast */
    --alert-warning-text-alt: #856404;
    --alert-warning-border-alt: #ffeeba;

    --sub-section-bg: #efe0c6;
    --sub-section-border: #d3c0a5;

    --debug-bg: #efe0c6;
    --debug-border: #d3c0a5;
    --debug-text-color: #6f4e28;
    --debug-pre-bg: #fdf3e3;
    --debug-pre-text: #5b4636;

    --link-color: var(--button-primary-bg);
    --link-hover-color: var(--button-primary-hover-bg);
    
    --preview-body-bg: #e0d5c1; /* Sepia preview page background */
    --preview-controls-bg: rgba(239, 224, 198, 0.85); /* Lighter sepia controls */
    --preview-page-bg: #f5f0e5; /* Sepia pages */
    --preview-page-border: #c0a080;
    --preview-page-text-color: #5b4636;
    --preview-page-header-color: #704214;
    --preview-nav-button-bg: #5a7d48; /* Muted green for nav */
    --preview-nav-button-hover-bg: #486838;
}
/* Ensure .form-control inputs within specific sections also adopt theme variables */
.ai-generation-form input[type="text"].form-control,
.export-settings-section input[type="number"].form-control,
.export-settings-section select.form-control {
    background-color: var(--input-bg-color);
    color: var(--input-text-color);
    border: 1px solid var(--border-color-dark);
}

/* Specificity for button text colors that need to change with themes */
.btn-warning { color: var(--button-warning-text); }
/* (Add other specific button text color overrides if needed) */

/* Ensure the main form inputs and selects follow the theme */
.container > .form-group input[type="text"],
.container > .form-group input[type="number"],
.container > .form-group select,
.container > .form-group textarea {
    background-color: var(--input-bg-color);
    color: var(--input-text-color);
    border: 1px solid var(--border-color);
}
.container > .form-group input[type="text"]:focus,
.container > .form-group input[type="number"]:focus,
.container > .form-group select:focus,
.container > .form-group textarea:focus {
    border-color: var(--input-focus-border-color);
}

/* Ensure KDP guidance section text color is also themed */
.kdp-guidance, .kdp-guidance p, .kdp-guidance ul li {
    color: var(--text-color);
}
.kdp-guidance h4 { color: var(--header-text-color); border-bottom-color: var(--border-color-dark); }
.kdp-guidance h5 { color: var(--debug-text-color); } /* Re-using a suitable variable */

/* Ensure editable content also follows theme */
.editable-title.chapter-title-input,
.editable-content.chapter-content-textarea {
    background-color: var(--input-bg-color);
    color: var(--input-text-color);
    border-color: var(--border-color-dark);
}
/* Ensure editor-specific styles for sections and borders use variables */
.chapter-editor { background-color: var(--sub-section-bg); border-color: var(--sub-section-border); }
.image-management-area { background-color: var(--sub-section-bg); border-color: var(--sub-section-border); }
.image-management-area h3, .image-management-area h4 { border-bottom-color: var(--border-color-light); }
.upload-form, .ai-generation-form { background-color: var(--container-bg-color); border-color: var(--border-color-light); }

.export-settings-section { background-color: var(--sub-section-bg); border-color: var(--sub-section-border); }
.export-settings-section h4 { border-bottom-color: var(--border-color-dark); }
.export-settings-section fieldset { border-color: var(--border-color-dark); }
.export-settings-section legend { color: var(--text-color); }

/* Ensure button text colors are maintained or updated as needed */
.btn-secondary { color: var(--button-secondary-text); }
.btn-ai-generate { color: var(--button-ai-generate-text); }

/* Ensure flash message text colors are also themed if their backgrounds change significantly */
.flash-messages .alert-success { background-color: var(--alert-success-bg); color: var(--alert-success-text); border-color: var(--alert-success-border); }
.flash-messages .alert-error   { background-color: var(--alert-error-bg);   color: var(--alert-error-text);   border-color: var(--alert-error-border);   }
.flash-messages .alert-info    { background-color: var(--alert-info-bg);    color: var(--alert-info-text);    border-color: var(--alert-info-border);    }
.flash-messages .alert-warning { background-color: var(--alert-warning-bg-alt); color: var(--alert-warning-text-alt); border-color: var(--alert-warning-border-alt); }

/* Theme switcher styles */
.theme-switcher {
    position: fixed;
    top: 15px;
    right: 15px;
    padding: 8px;
    background-color: var(--container-bg-color);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 1000;
}
.theme-switcher label {
    margin-right: 8px;
    font-size: 0.9em;
    color: var(--label-text-color);
}
.theme-switcher select {
    padding: 5px;
    border-radius: 3px;
    border: 1px solid var(--border-color-dark);
    background-color: var(--input-bg-color);
    color: var(--input-text-color);
    font-size: 0.9em;
}

/* Global Header Styles */
.app-header {
    background-color: var(--container-bg-color); /* Use a theme variable */
    padding: 10px 20px;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-bottom: 20px; /* Space below header */
    width: 100%; /* Make header span full width */
    box-sizing: border-box;
    position: sticky; /* Make header sticky */
    top: 0;
    z-index: 1020; /* Ensure it's above most other content */
}

.app-header .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px; /* Max width for header content */
    margin: 0 auto; /* Center header content */
    flex-wrap: wrap; /* Allow header items to wrap */
}

/* Ensure title and nav/theme switcher have some space on wrap */
.app-header .app-title-link, .app-header .app-nav, .app-header .theme-switcher {
    margin-bottom: 5px; /* Add some bottom margin for wrapped items */
}


.app-header .app-title-link {
    text-decoration: none;
}

.app-header .app-title {
    font-size: 1.8em;
    color: var(--header-text-color);
    margin: 0; /* Remove default h1 margin */
}

.app-header .app-nav {
    display: flex;
    gap: 15px;
}

.app-header .nav-link {
    text-decoration: none;
    color: var(--link-color);
    font-size: 1em;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.app-header .nav-link:hover {
    background-color: var(--button-primary-hover-bg);
    color: var(--button-primary-text);
}

/* Theme switcher inside header */
.app-header .theme-switcher {
    position: static; /* Override fixed positioning if it was there */
    padding: 0;
    background-color: transparent;
    border: none;
    box-shadow: none;
}

.app-header .theme-switcher label {
    color: var(--label-text-color);
}

.app-header .theme-switcher select {
    background-color: var(--input-bg-color);
    color: var(--input-text-color);
    border: 1px solid var(--border-color-dark);
}

/* Adjust body padding if header is sticky/fixed and has a fixed height */
body {
    /* padding-top: 70px; /* Example: Adjust if header height is fixed to 70px if header is not sticky */
}

.page-title { /* For H1s that are main titles of a page, not the app title */
    color: var(--header-text-color);
    text-align: center;
    margin-top: 0; /* Adjust if header is sticky and has padding */
    margin-bottom: 25px;
}

/* Image Preview Class */
.img-preview {
    max-width: 200px; /* Default, can be overridden */
    height: auto;
    display: block;
    margin: 10px auto; /* Center by default */
    border: 1px solid var(--border-color);
    padding: 4px;
    background-color: var(--container-bg-color);
    border-radius: 4px;
}
/* Ensure image previews in image management sections are responsive within their containers */
.image-management-area .image-preview img {
    max-width: 100%; /* Make image responsive within its preview container */
    width: auto; /* Maintain aspect ratio, but ensure it doesn't exceed container */
    max-height: 200px; /* Limit height for previews */
    object-fit: contain;
}


/* Responsive adjustments for controls bar in book_preview.html */
.controls-bar {
    align-items: center; /* Ensure vertical alignment when wrapped */
}
.controls-bar > div { /* Target direct children: .flipbook-nav, .tts-controls, .background-selector-form */
    margin-bottom: 10px; /* Add margin for stacking */
}
@media (max-width: 768px) {
    .controls-bar {
        flex-direction: column; /* Stack controls vertically on small screens */
        align-items: stretch; /* Make control groups take full width */
    }
    .controls-bar > div {
        width: 100%;
        text-align: center; /* Center content of each control group */
        margin-bottom: 15px;
    }
    .controls-bar .flipbook-nav, 
    .controls-bar .tts-controls, 
    .controls-bar .background-selector-form form {
        display: flex;
        flex-direction: column; /* Stack items within each group */
        align-items: center;
    }
    .controls-bar .tts-controls > * { /* Stack TTS elements nicely */
        margin-bottom: 5px;
    }
    .app-header .header-content { /* Simpler header stacking */
        flex-direction: column;
        align-items: flex-start;
    }
    .app-header .app-nav {
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .app-header .theme-switcher {
        align-self: flex-start; /* Align theme switcher to the left when stacked */
    }
}

/* Text alignment helper */
.text-center {
    text-align: center;
}
