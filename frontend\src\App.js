import React, { useState } from 'react';
import axios from 'axios';
import './App.css';

// In a real app, this might be fetched from the API
const WORKFLOWS = [
  { id: 'WF-1', title: 'Text klarer/besser formulieren' },
  { id: 'WF-2', title: 'Spenden aus Bild/PDF extrahieren & beurteilen (LU)' },
  { id: 'WF-3', title: 'Einsprache analysieren & Entscheid entwerfen' },
  { id: 'WF-4', title: 'Liegenschaftsunterhalt prüfen & Abzug berechnen' },
];

const API_BASE_URL = 'http://localhost:8000'; // Assuming the backend runs on port 8000

function App() {
  const [workflowId, setWorkflowId] = useState(WORKFLOWS[0].id);
  const [textInput, setTextInput] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleFileChange = (event) => {
    setSelectedFile(event.target.files[0]);
    setTextInput(''); // Clear text input if a file is selected
  };

  const handleTextChange = (event) => {
    setTextInput(event.target.value);
    setSelectedFile(null); // Clear file input if text is entered
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    if (!textInput && !selectedFile) {
      setError('Bitte geben Sie entweder einen Text ein oder wählen Sie eine Datei aus.');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      let response;
      if (selectedFile) {
        const formData = new FormData();
        formData.append('workflow_id', workflowId);
        formData.append('file', selectedFile);

        response = await axios.post(`${API_BASE_URL}/execute-workflow/file`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      } else {
        response = await axios.post(`${API_BASE_URL}/execute-workflow/text`, {
          workflow_id: workflowId,
          text_input: textInput,
        });
      }
      setResult(response.data);
    } catch (err) {
      const errorMessage = err.response?.data?.detail || err.message || 'Ein unbekannter Fehler ist aufgetreten.';
      setError(`Fehler: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h1>KI-Assistent für Steuerfachpersonen (Luzern)</h1>
      <div className="app-container">
        <div className="form-container">
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="workflow-select">1. Workflow auswählen</label>
              <select id="workflow-select" value={workflowId} onChange={(e) => setWorkflowId(e.target.value)}>
                {WORKFLOWS.map(wf => (
                  <option key={wf.id} value={wf.id}>{wf.title}</option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="text-input">2. Eingabe (Text ODER Datei)</label>
              <textarea
                id="text-input"
                placeholder="Text hier einfügen..."
                value={textInput}
                onChange={handleTextChange}
                disabled={!!selectedFile}
              />
            </div>

            <div className="form-group">
              <input
                type="file"
                onChange={handleFileChange}
                disabled={!!textInput}
                accept=".pdf,.png,.jpg,.jpeg"
              />
            </div>

            <button type="submit" disabled={loading}>
              {loading ? 'Verarbeite...' : 'Workflow ausführen'}
            </button>
          </form>
        </div>

        <div className="result-container">
          <h2>Ergebnis</h2>
          {loading && <p className="loading-message">Bitte warten, Anfrage wird verarbeitet...</p>}
          {error && <p className="error-message">{error}</p>}
          {result && <ResultDisplay data={result} />}
        </div>
      </div>
    </div>
  );
}

// A sub-component to display results in a structured way
const ResultDisplay = ({ data }) => {
  if (!data) return null;

  // Helper to render a table for WF-2
  const renderTable = (title, items) => (
    <>
      <h4>{title}</h4>
      <table>
        <thead>
          <tr>
            <th>Empfänger</th>
            <th>Betrag</th>
            <th>Begründung</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item, index) => (
            <tr key={index}>
              <td>{item.empfaenger}</td>
              <td>{item.betrag}</td>
              <td>{item.begruendung}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </>
  );

  // Helper to render totalisation for WF-2
  const renderTotalisation = (totals) => (
     <>
      <h4>Totalisation</h4>
      <p>
        <strong>Summe Abzugsfähig:</strong> {totals.summe_abzugsfähig}<br/>
        <strong>Summe Nicht abzugsfähig:</strong> {totals.summe_nicht_abzugsfähig}<br/>
        <strong>Gesamt:</strong> {totals.gesamt}
      </p>
    </>
  )

  return (
    <div>
      <div className="result-section">
        <h3>Kurzfazit</h3>
        <p>{data.kurzfazit}</p>
      </div>

      <div className="result-section">
        <h3>Ergebnis</h3>
        {/* Custom rendering for WF-2 results */}
        {data.ergebnis.abzugsfähig && renderTable("Abzugsfähig", data.ergebnis.abzugsfähig)}
        {data.ergebnis.nicht_abzugsfähig && renderTable("Nicht Abzugsfähig", data.ergebnis.nicht_abzugsfähig)}
        {data.ergebnis.totalisation && renderTotalisation(data.ergebnis.totalisation)}

        {/* Default rendering for other workflows */}
        {data.ergebnis.ueberarbeitete_fassung && (
          <>
            <h4>Überarbeitete Fassung</h4>
            <pre>{data.ergebnis.ueberarbeitete_fassung}</pre>
            {data.ergebnis.alternativen && (
              <>
                <h5>Alternativen</h5>
                <ul>
                  {data.ergebnis.alternativen.map((alt, i) => <li key={i}>{alt}</li>)}
                </ul>
              </>
            )}
          </>
        )}
      </div>

      <div className="result-section">
        <h3>Text für Steuerkunde</h3>
        <pre>{data.text_fuer_kunde}</pre>
      </div>

      <div className="result-section">
        <h3>Interne Notizen/Begründung</h3>
        <p>{data.interne_notizen}</p>
      </div>

      {data.rueckfragen && data.rueckfragen.length > 0 && (
        <div className="result-section">
          <h3>Rückfragen</h3>
          <ul>
            {data.rueckfragen.map((q, index) => (
              <li key={index}>{q}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};


export default App;
