import os
from flask import Flask

app = Flask(__name__)

# Secret key for session management (important for flash messages and session data)
app.secret_key = os.urandom(24) # Replace with a fixed, strong secret key in production

# Configuration for file uploads
UPLOAD_FOLDER = os.path.join(app.static_folder, 'uploads')
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024 # 16MB max upload size

# Ensure the upload folder exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

from app import routes
