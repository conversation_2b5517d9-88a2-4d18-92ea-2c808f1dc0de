document.addEventListener('DOMContentLoaded', function() {
    // Theme switcher logic - uses 'themeSelectGlobal' from _header.html
    const themeSelectGlobal = document.getElementById('themeSelectGlobal');
    const currentTheme = localStorage.getItem('theme') || 'light'; // Default to light

    function applyTheme(themeName) {
        document.body.classList.remove('theme-dark', 'theme-sepia'); // Remove any existing theme classes
        if (themeName === 'dark') {
            document.body.classList.add('theme-dark');
        } else if (themeName === 'sepia') {
            document.body.classList.add('theme-sepia');
        }
        // 'light' theme is applied by having no specific theme class or by removing others.
        
        if (themeSelectGlobal) {
            themeSelectGlobal.value = themeName;
        }
    }

    function saveThemePreference(themeName) {
        localStorage.setItem('theme', themeName);
    }

    // Apply saved theme on initial load
    applyTheme(currentTheme);

    if (themeSelectGlobal) {
        themeSelectGlobal.addEventListener('change', function() {
            const selectedTheme = themeSelectGlobal.value;
            applyTheme(selectedTheme);
            saveThemePreference(selectedTheme);
        });
    }

    // Handle custom input display for genre and writing style in index.html
    const genreSelect = document.getElementById('genre');
    const genreCustomInput = document.getElementById('genre_custom');
    const writingStyleSelect = document.getElementById('writing_style');
    const writingStyleCustomInput = document.getElementById('writing_style_custom');

    if (genreSelect && genreCustomInput) {
        genreSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                genreCustomInput.style.display = 'block';
                genreCustomInput.setAttribute('required', 'required');
            } else {
                genreCustomInput.style.display = 'none';
                genreCustomInput.removeAttribute('required');
                genreCustomInput.value = ''; // Clear value
            }
        });
        // Initial check in case of page reload with 'custom' selected
        if (genreSelect.value === 'custom') {
             genreCustomInput.style.display = 'block';
             genreCustomInput.setAttribute('required', 'required');
        }
    }

    if (writingStyleSelect && writingStyleCustomInput) {
        writingStyleSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                writingStyleCustomInput.style.display = 'block';
                writingStyleCustomInput.setAttribute('required', 'required');
            } else {
                writingStyleCustomInput.style.display = 'none';
                writingStyleCustomInput.removeAttribute('required');
                writingStyleCustomInput.value = ''; // Clear value
            }
        });
        // Initial check
        if (writingStyleSelect.value === 'custom') {
            writingStyleCustomInput.style.display = 'block';
            writingStyleCustomInput.setAttribute('required', 'required');
        }
    }
    
    // Basic JS to make alert messages dismissible (optional, but was in edit_book.html)
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        alert.addEventListener('click', function() {
            this.style.display = 'none';
        });
    });

    // The "Add Another Chapter Title" button functionality is not part of this subtask,
    // but if it were, its event listener would be here.
    // Example:
    // const addChapterButton = document.getElementById('addChapterTitleButton');
    // if (addChapterButton) {
    //     addChapterButton.addEventListener('click', function() {
    //         // Logic to add a new chapter title input field
    //         console.log("Add chapter title button clicked - functionality not implemented in this task.");
    //     });
    // }

});
