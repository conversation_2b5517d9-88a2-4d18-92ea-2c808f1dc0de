import os
import io # For BytesIO
import uuid # For EPUB identifier
import copy # For deepcopy
from flask import render_template, request, Markup, url_for, current_app, session, redirect, flash, send_file
from werkzeug.utils import secure_filename
from weasyprint import HTML, CSS # WeasyPrint import
from ebooklib import epub # EbookLib import
from app import app

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
AI_PLACEHOLDER_DIR = 'ai_placeholders'
BACKGROUND_PATTERNS_DIR = 'background_patterns'
EPUB_STYLE_FILENAME = 'epub_style.css' 

DEFAULT_EXPORT_SETTINGS = {
    'pdf_page_size': 'A4',
    'pdf_margin_top': '20', # mm
    'pdf_margin_bottom': '20', # mm
    'pdf_margin_left': '20', # mm
    'pdf_margin_right': '20', # mm
    'font_family': 'Times New Roman, serif',
    'font_size': '12' # pt
}

PDF_PAGE_SIZE_OPTIONS = {
    'A4': 'A4',
    'A5': 'A5',
    'Letter': 'letter',
    '6x9 inches': '6in 9in',
    '5x8 inches': '5in 8in'
}

FONT_FAMILY_OPTIONS = {
    'Times New Roman, serif': 'Times New Roman (Serif)',
    'Arial, sans-serif': 'Arial (Sans-Serif)',
    'Georgia, serif': 'Georgia (Serif)',
    'Verdana, sans-serif': 'Verdana (Sans-Serif)',
    'Courier New, monospace': 'Courier New (Monospace)'
}


BACKGROUND_CHOICES = {
    "none": None,
    "paper": os.path.join(BACKGROUND_PATTERNS_DIR, 'pattern_paper.png'),
    "dark_wood": os.path.join(BACKGROUND_PATTERNS_DIR, 'pattern_dark_wood.png'),
    "dots": os.path.join(BACKGROUND_PATTERNS_DIR, 'pattern_dots.png'),
    "default": os.path.join(BACKGROUND_PATTERNS_DIR, 'default_background.png')
}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_book_data_from_session():
    return session.get('current_book_data')

def get_export_settings():
    # Return a copy to prevent modifying session defaults indirectly
    return session.get('export_settings', DEFAULT_EXPORT_SETTINGS.copy())


def get_asset_path(filename):
    if not filename: return None
    return os.path.join(current_app.static_folder, filename)

@app.context_processor
def utility_processor():
    # Make these available to all templates for the forms
    return dict(
        get_asset_url=get_asset_path, 
        PDF_PAGE_SIZE_OPTIONS=PDF_PAGE_SIZE_OPTIONS,
        FONT_FAMILY_OPTIONS=FONT_FAMILY_OPTIONS,
        export_settings=get_export_settings() # Ensure current settings are available to pre-fill form
    )

def simulate_llm_api_call(prompt_text, form_data):
    genre = form_data.get('genre', 'adventure')
    if genre == 'custom': genre = form_data.get('genre_custom', 'unique adventure')
    default_chapter_title = "Chapter 1: The Journey Begins"
    chapter_title_1 = form_data.get('chapter_title_1') if form_data.get('chapter_title_1', '').strip() else default_chapter_title
    chapter_content = f"""In the sprawling kingdom of Eldoria, a land of breathtaking {genre} landscapes and ancient mysteries, our story unfolds.
The air was thick with anticipation, and the scent of adventure (or perhaps freshly baked bread from a nearby village) lingered.
Our hero, a brave soul known for their wit and {form_data.get('writing_style', 'eloquent')} prose, was about to embark on a quest.
This quest was aimed at {form_data.get('target_audience', 'all who seek excitement')}, focusing on {form_data.get('content_focus', 'epic discoveries')}.
The chronicles say this tale would span {form_data.get('num_chapters', 'many')} chapters, each more thrilling than the last.
"""
    return {
        'book_title': f"The Epic of {genre.capitalize()}",
        'chapters': [{'title': chapter_title_1, 'content': chapter_content, 'image_url': None}],
        'prompt_used': prompt_text, 'cover_image_url': None,
        'background_pattern': BACKGROUND_CHOICES["default"]
    }

def generate_book_content(form_data):
    genre = form_data.get('genre');
    if genre == 'custom': genre = form_data.get('genre_custom', 'custom genre')
    target_audience = form_data.get('target_audience', 'a general audience')
    content_focus = form_data.get('content_focus', 'interesting topics')
    writing_style = form_data.get('writing_style')
    if writing_style == 'custom': writing_style = form_data.get('writing_style_custom', 'a unique style')
    num_chapters = form_data.get('num_chapters', 'several')
    chapter_title_1 = form_data.get('chapter_title_1', '')
    prompt = f"""Please help me start writing a new book with the following characteristics:
Genre: {genre} Target Audience: {target_audience} Key Content Focus Points/Keywords: {content_focus}
Desired Writing Style: {writing_style} Number of Chapters: {num_chapters}
"""
    if chapter_title_1: prompt += f"The title for the first chapter should be: \"{chapter_title_1}\".\n"
    else: prompt += "Please suggest a suitable title for the first chapter.\n"
    prompt += f"\nPlease generate the content for the first chapter of this {genre} book. "
    prompt += "The tone should be engaging and appropriate for the specified genre and target audience."
    return simulate_llm_api_call(prompt, form_data)

def simulate_ai_image_generation(prompt, style):
    print(f"Simulating AI image generation for prompt: '{prompt}' with style: '{style}'")
    style_map = {'cartoon': 'placeholder_cartoon.png', 'realistic': 'placeholder_realistic.png', 'fantasy': 'placeholder_fantasy.png'}
    return os.path.join(AI_PLACEHOLDER_DIR, style_map.get(style.lower(), 'placeholder_default.png'))

@app.route('/')
def index(): return render_template('index.html')

@app.route('/generate-book', methods=['POST'])
def generate_book_and_redirect():
    if request.method == 'POST':
        form_data = request.form.to_dict()
        book_data_structured = generate_book_content(form_data)
        if 'background_pattern' not in book_data_structured:
             book_data_structured['background_pattern'] = BACKGROUND_CHOICES["default"]
        session['current_book_data'] = book_data_structured
        # Initialize export settings if not already in session
        if 'export_settings' not in session:
            session['export_settings'] = DEFAULT_EXPORT_SETTINGS.copy()
        return redirect(url_for('edit_book_page'))
    return redirect(url_for('index'))

@app.route('/edit-book', methods=['GET'])
def edit_book_page():
    book_data = get_book_data_from_session()
    if not book_data: flash("Please generate a book first.", "info"); return redirect(url_for('index'))
    # Ensure export_settings are in session for the template context processor
    if 'export_settings' not in session:
        session['export_settings'] = DEFAULT_EXPORT_SETTINGS.copy()
    return render_template('edit_book.html', book_data=book_data)


@app.route('/save-export-settings', methods=['POST'])
def save_export_settings():
    if request.method == 'POST':
        current_settings = get_export_settings() # Get a copy of current or default settings

        # Update with validated form data
        current_settings['pdf_page_size'] = request.form.get('pdf_page_size', current_settings['pdf_page_size'])
        if current_settings['pdf_page_size'] not in PDF_PAGE_SIZE_OPTIONS: # Basic validation
            current_settings['pdf_page_size'] = DEFAULT_EXPORT_SETTINGS['pdf_page_size']

        current_settings['pdf_margin_top'] = request.form.get('pdf_margin_top', current_settings['pdf_margin_top'])
        current_settings['pdf_margin_bottom'] = request.form.get('pdf_margin_bottom', current_settings['pdf_margin_bottom'])
        current_settings['pdf_margin_left'] = request.form.get('pdf_margin_left', current_settings['pdf_margin_left'])
        current_settings['pdf_margin_right'] = request.form.get('pdf_margin_right', current_settings['pdf_margin_right'])
        
        current_settings['font_family'] = request.form.get('font_family', current_settings['font_family'])
        if current_settings['font_family'] not in FONT_FAMILY_OPTIONS: # Basic validation
            current_settings['font_family'] = DEFAULT_EXPORT_SETTINGS['font_family']
            
        current_settings['font_size'] = request.form.get('font_size', current_settings['font_size'])

        # TODO: Add more robust validation for numeric types and ranges for margins/font size
        try:
            float(current_settings['pdf_margin_top'])
            float(current_settings['pdf_margin_bottom'])
            float(current_settings['pdf_margin_left'])
            float(current_settings['pdf_margin_right'])
            float(current_settings['font_size'])
        except ValueError:
            flash("Invalid numeric value for margins or font size.", "error")
            # Revert to defaults for numeric fields if validation fails
            current_settings['pdf_margin_top'] = DEFAULT_EXPORT_SETTINGS['pdf_margin_top']
            # ... (revert other numeric fields similarly)
            current_settings['font_size'] = DEFAULT_EXPORT_SETTINGS['font_size']


        session['export_settings'] = current_settings
        flash("Export settings saved successfully!", "success")
    return redirect(url_for('edit_book_page'))


# ... (upload_cover_image, upload_chapter_image, generate_ai_cover_image, generate_ai_chapter_image routes are unchanged) ...
@app.route('/upload-cover-image', methods=['POST'])
def upload_cover_image():
    book_data = get_book_data_from_session()
    if not book_data: flash("Book data not found.", "error"); return redirect(url_for('index'))
    if 'image' not in request.files: flash('No file part.', 'error'); return redirect(url_for('edit_book_page'))
    file = request.files['image']
    if file.filename == '': flash('No selected file.', 'warning'); return redirect(url_for('edit_book_page'))
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        try:
            file.save(filepath)
            book_data['cover_image_url'] = os.path.join('uploads', filename)
            session['current_book_data'] = book_data 
            flash('Cover image uploaded!', 'success')
        except Exception as e: flash(f'Error saving file: {e}', 'error')
    else: flash('Invalid file type.', 'error')
    return redirect(url_for('edit_book_page'))

@app.route('/upload-chapter-image/<int:chapter_index>', methods=['POST'])
def upload_chapter_image(chapter_index):
    book_data = get_book_data_from_session()
    if not book_data: flash("Book data not found.", "error"); return redirect(url_for('index'))
    if not 0 <= chapter_index < len(book_data['chapters']): flash('Invalid chapter index.', 'error'); return redirect(url_for('edit_book_page'))
    if 'image' not in request.files: flash('No file part.', 'error'); return redirect(url_for('edit_book_page'))
    file = request.files['image']
    if file.filename == '': flash('No selected file.', 'warning'); return redirect(url_for('edit_book_page'))
    if file and allowed_file(file.filename):
        filename = secure_filename(f"chapter_{chapter_index+1}_{file.filename}")
        filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
        try:
            file.save(filepath)
            book_data['chapters'][chapter_index]['image_url'] = os.path.join('uploads', filename)
            session['current_book_data'] = book_data
            flash(f'Image for Chapter {chapter_index + 1} uploaded!', 'success')
        except Exception as e: flash(f'Error saving file: {e}', 'error')
    else: flash('Invalid file type.', 'error')
    return redirect(url_for('edit_book_page'))

@app.route('/generate-ai-cover-image', methods=['POST'])
def generate_ai_cover_image():
    book_data = get_book_data_from_session()
    if not book_data: flash("Book data not found.", "error"); return redirect(url_for('index'))
    image_style = request.form.get('image_style', 'default')
    image_prompt = request.form.get('image_prompt', 'A beautiful book cover')
    if not image_prompt.strip(): flash("Prompt for cover image needed.", "warning"); return redirect(url_for('edit_book_page'))
    generated_image_path = simulate_ai_image_generation(image_prompt, image_style)
    book_data['cover_image_url'] = generated_image_path
    session['current_book_data'] = book_data
    flash(f'AI Cover Image (Simulated) with style "{image_style}"!', 'success')
    return redirect(url_for('edit_book_page'))

@app.route('/generate-ai-chapter-image/<int:chapter_index>', methods=['POST'])
def generate_ai_chapter_image(chapter_index):
    book_data = get_book_data_from_session()
    if not book_data: flash("Book data not found.", "error"); return redirect(url_for('index'))
    if not 0 <= chapter_index < len(book_data['chapters']): flash('Invalid chapter index.', 'error'); return redirect(url_for('edit_book_page'))
    image_style = request.form.get('image_style', 'default')
    image_prompt = request.form.get('image_prompt', f"Illustration for Chapter {chapter_index + 1}")
    if not image_prompt.strip(): flash(f"Prompt for Chapter {chapter_index+1} image needed.", "warning"); return redirect(url_for('edit_book_page'))
    generated_image_path = simulate_ai_image_generation(image_prompt, image_style)
    book_data['chapters'][chapter_index]['image_url'] = generated_image_path
    session['current_book_data'] = book_data
    flash(f'AI Image for Chapter {chapter_index + 1} (Simulated) with style "{image_style}"!', 'success')
    return redirect(url_for('edit_book_page'))


@app.route('/book-preview', methods=['GET'])
def book_preview_page():
    book_data = get_book_data_from_session()
    if not book_data: flash("No book data. Generate a book first.", "warning"); return redirect(url_for('index'))
    if 'background_pattern' not in book_data: book_data['background_pattern'] = BACKGROUND_CHOICES["default"]
    return render_template('book_preview.html', book_data=book_data, background_options=BACKGROUND_CHOICES)

@app.route('/set-book-background', methods=['POST'])
def set_book_background():
    book_data = get_book_data_from_session()
    if not book_data: flash("Book data not found.", "error"); return redirect(url_for('index'))
    selected_choice_key = request.form.get('background_choice')
    if selected_choice_key in BACKGROUND_CHOICES:
        book_data['background_pattern'] = BACKGROUND_CHOICES[selected_choice_key]
        session['current_book_data'] = book_data
        flash(f"Background set to '{selected_choice_key}'.", "success")
    else: flash("Invalid background choice.", "error")
    return redirect(url_for('book_preview_page'))

@app.route('/export-pdf')
def export_pdf():
    book_data_session = get_book_data_from_session()
    if not book_data_session: flash("No book data to export.", "warning"); return redirect(url_for('index'))
    
    book_data = copy.deepcopy(book_data_session)
    settings = get_export_settings()

    if book_data.get('cover_image_url'): book_data['cover_image_url_abs'] = get_asset_path(book_data['cover_image_url'])
    for chapter in book_data.get('chapters', []):
        if chapter.get('image_url'): chapter['image_url_abs'] = get_asset_path(chapter['image_url'])
    
    # Generate dynamic CSS for PDF
    dynamic_css_string = f"""
        @page {{
            size: {settings['pdf_page_size']};
            margin-top: {settings['pdf_margin_top']}mm;
            margin-bottom: {settings['pdf_margin_bottom']}mm;
            margin-left: {settings['pdf_margin_left']}mm;
            margin-right: {settings['pdf_margin_right']}mm;
        }}
        body {{
            font-family: {settings['font_family']};
            font-size: {settings['font_size']}pt;
        }}
    """
    
    html_string = render_template('pdf_template.html', book_data=book_data, get_asset_url=get_asset_path)
    
    base_css_path = get_asset_path('pdf_style.css')
    base_css = CSS(base_css_path)
    dynamic_css = CSS(string=dynamic_css_string)
    
    html_doc = HTML(string=html_string, base_url=current_app.static_folder) 
    pdf_bytes = html_doc.write_pdf(stylesheets=[base_css, dynamic_css])
    
    book_title_slug = secure_filename(book_data.get('book_title', 'book')).lower().replace('_', '-') or "exported-book"
    return send_file(io.BytesIO(pdf_bytes), mimetype='application/pdf', as_attachment=True, download_name=f'{book_title_slug}.pdf')

@app.route('/export-epub')
def export_epub():
    book_data = get_book_data_from_session()
    if not book_data: flash("No book data to export.", "warning"); return redirect(url_for('index'))

    settings = get_export_settings()
    book = epub.EpubBook()
    book_title = book_data.get('book_title', 'Untitled Book')
    
    book.set_identifier(str(uuid.uuid4())); book.set_title(book_title); book.set_language('en')
    book.add_author('AI Book Creator (Simulated)')

    epub_cover_item = None
    if book_data.get('cover_image_url'):
        cover_image_abs_path = get_asset_path(book_data['cover_image_url'])
        if cover_image_abs_path and os.path.exists(cover_image_abs_path):
            with open(cover_image_abs_path, 'rb') as f: cover_image_content = f.read()
            img_ext = book_data['cover_image_url'].split('.')[-1].lower()
            cover_image_filename = f"cover.{img_ext}"
            epub_cover_item = epub.EpubImage(uid='cover_image', file_name=f"images/{cover_image_filename}", 
                                             media_type=f"image/{img_ext}", content=cover_image_content)
            book.add_item(epub_cover_item)
            book.set_cover(f"images/{cover_image_filename}", cover_image_content)

    try:
        with open(get_asset_path(EPUB_STYLE_FILENAME), 'r', encoding='utf-8') as f:
            css_content = f.read()
    except FileNotFoundError:
        css_content = "body { font-family: sans-serif; font-size: 1em; } h1, h2 { text-align: center; } p { margin-bottom: 1em; text-align: justify; }"
    
    # Append dynamic font settings to EPUB CSS
    dynamic_epub_css = f"\nbody {{ font-family: {settings['font_family']}; font-size: {settings['font_size']}pt; }}\n"
    final_css_content = css_content + dynamic_epub_css
    
    style_item = epub.EpubItem(uid="style_default", file_name=f"css/{EPUB_STYLE_FILENAME}", 
                               media_type="text/css", content=final_css_content.encode('utf-8'))
    book.add_item(style_item)

    epub_chapters = []; spine_items = ['nav']
    if epub_cover_item: pass 

    for idx, chapter_data in enumerate(book_data.get('chapters', [])):
        chapter_title = chapter_data.get('title', f'Chapter {idx+1}')
        chapter_content_raw = chapter_data.get('content', '')
        chapter_image_url = chapter_data.get('image_url')
        xhtml_content = f'<h1>{chapter_title}</h1>'
        if chapter_image_url:
            chapter_image_abs_path = get_asset_path(chapter_image_url)
            if chapter_image_abs_path and os.path.exists(chapter_image_abs_path):
                with open(chapter_image_abs_path, 'rb') as f: ch_img_content = f.read()
                ch_img_ext = chapter_image_url.split('.')[-1].lower()
                ch_img_filename = f"chapter_{idx+1}_image.{ch_img_ext}"
                epub_ch_image_item = epub.EpubImage(uid=f'chap_img_{idx}', file_name=f"images/{ch_img_filename}",
                                                    media_type=f"image/{ch_img_ext}", content=ch_img_content)
                book.add_item(epub_ch_image_item)
                xhtml_content += f'<div style="text-align:center;"><img src="images/{ch_img_filename}" alt="{chapter_title} illustration" style="max-width:80%; height:auto;"/></div>'
        paragraphs = chapter_content_raw.split('\n')
        for p_text in paragraphs:
            if p_text.strip(): xhtml_content += f'<p>{p_text.strip()}</p>'
            else: xhtml_content += '<p>&nbsp;</p>'
        epub_chapter = epub.EpubHtml(title=chapter_title, file_name=f'chap_{idx+1}.xhtml', lang='en')
        epub_chapter.content = f'<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html><html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops"><head><title>{chapter_title}</title><link rel="stylesheet" type="text/css" href="css/{EPUB_STYLE_FILENAME}" /></head><body>{xhtml_content}</body></html>'
        book.add_item(epub_chapter); epub_chapters.append(epub_chapter); spine_items.append(epub_chapter)
    
    book.toc = tuple(epub_chapters)
    book.add_item(epub.EpubNcx()); book.add_item(epub.EpubNav())
    book.spine = spine_items
    
    epub_buffer = io.BytesIO(); epub.write_epub(epub_buffer, book, {})
    epub_bytes = epub_buffer.getvalue(); epub_buffer.close()
    book_title_slug = secure_filename(book_title).lower().replace('_', '-') or "exported-book"
    return send_file(io.BytesIO(epub_bytes), mimetype='application/epub+zip', as_attachment=True, download_name=f'{book_title_slug}.epub')
