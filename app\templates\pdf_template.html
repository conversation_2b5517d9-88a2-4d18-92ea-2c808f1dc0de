<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{ book_data.book_title }} - PDF</title>
    <link rel="stylesheet" href="{{ get_asset_url('pdf_style.css') }}">
</head>
<body>
    <!-- Cover Page -->
    <div class="pdf-page cover-page">
        {% if book_data.cover_image_url_abs %}
            <img src="{{ book_data.cover_image_url_abs }}" class="cover-image" alt="Cover Image">
        {% endif %}
        <h1 class="book-title">{{ book_data.book_title }}</h1>
    </div>

    <!-- Chapters -->
    {% for chapter in book_data.chapters %}
        <div class="pdf-page chapter-start-page">
            <h2 class="chapter-title">{{ chapter.title }}</h2>
            {% if chapter.image_url_abs %}
                <img src="{{ chapter.image_url_abs }}" class="chapter-image" alt="Image for {{ chapter.title }}">
            {% endif %}
            <div class="chapter-content">
                {{ chapter.content|replace('\n', '<br>')|safe }}
            </div>
        </div>
    {% endfor %}
</body>
</html>
