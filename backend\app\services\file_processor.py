import fitz  # PyMuPDF
import pytesseract
from PIL import Image
import io
from fastapi import UploadFile, HTTPException

async def _process_pdf(contents: bytes) -> str:
    """Extracts text from a PDF file's contents."""
    text = ""
    try:
        with fitz.open(stream=contents, filetype="pdf") as doc:
            for page in doc:
                text += page.get_text()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {e}")
    return text

async def _process_image(contents: bytes) -> str:
    """Extracts text from an image file's contents using OCR."""
    try:
        image = Image.open(io.BytesIO(contents))
        text = pytesseract.image_to_string(image, lang='deu')  # Specify German language for OCR
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing image with OCR: {e}")
    return text

async def process_file(file: UploadFile) -> str:
    """
    Processes an uploaded file (PDF or image) and extracts text.
    """
    contents = await file.read()
    content_type = file.content_type

    if content_type == "application/pdf":
        return await _process_pdf(contents)
    elif content_type.startswith("image/"):
        return await _process_image(contents)
    else:
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {content_type}. Please upload a PDF or an image."
        )
