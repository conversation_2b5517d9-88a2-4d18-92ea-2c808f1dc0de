/* Basic Turn.js styling */
.flipbook {
    width: 800px; /* Default width, can be overridden by JS */
    height: 600px; /* Default height, can be overridden by JS */
    margin: 20px auto;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.flipbook .turn-page {
    background-color: #fff;
    background-size: cover;
    background-position: center;
    overflow: hidden; /* Important for content within pages */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 20px;
    box-sizing: border-box;
    border: 1px solid #ccc; /* For visibility of page boundaries */
}

/* Styling for content within a page */
.flipbook .page-content {
    width: 100%;
    height: 100%;
    overflow-y: auto; /* Allow scrolling for long content within a page */
    padding: 10px;
    box-sizing: border-box;
    text-align: left;
}

.flipbook .page-content h1, .flipbook .page-content h2 {
    text-align: center;
    margin-bottom: 15px;
}

.flipbook .page-content img {
    max-width: 80%;
    max-height: 40%; /* Adjust as needed */
    display: block;
    margin: 10px auto;
    border: 1px solid #eee;
}

.flipbook .page-content p {
    font-size: 1em;
    line-height: 1.6;
    text-align: justify;
    margin-bottom: 10px;
    white-space: pre-wrap; /* Respect newlines and spaces in content */
}

/* Specific styling for cover page */
.flipbook .cover-page {
    background-color: #f0f0f0; /* Different background for cover */
}

.flipbook .cover-page h1 {
    font-size: 2.5em;
    color: #333;
    margin-top: 20%;
}

.flipbook .cover-page img {
    max-width: 90%;
    max-height: 60%;
    margin-top: 20px;
}

/* For double page display, Turn.js might add classes like .odd, .even */
.flipbook .even {
    /* background-image: -webkit-gradient(linear, left top, right top, color-stop(0.95, #fff), color-stop(1, #dadada)); */
}

.flipbook .odd {
    /* background-image: -webkit-gradient(linear, right top, left top, color-stop(0.95, #fff), color-stop(1, #dadada)); */
}

/* Shadow for the page flip effect (optional, Turn.js might handle this) */
.flipbook .turn-page.shadow{
	-webkit-box-shadow:0 0 20px #ccc;
	-moz-box-shadow:0 0 20px #ccc;
	-ms-box-shadow:0 0 20px #ccc;
	-o-box-shadow:0 0 20px #ccc;
	box-shadow:0 0 20px #ccc;
}

/* Basic navigation (optional, Turn.js can be controlled via API) */
.flipbook-nav {
    text-align: center;
    margin: 10px 0;
}
.flipbook-nav button {
    padding: 5px 10px;
    margin: 0 5px;
}
