<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Preview - {{ book_data.book_title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='lib/turnjs/turn.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}"> <!-- Main stylesheet for body, etc. -->
    <style>
        body {
            background-color: #ccc; 
            display: block; 
            padding-top: 20px;
            padding-bottom: 20px;
            transition: background-image 0.5s ease-in-out, background-color 0.5s ease-in-out;
            font-family: Arial, sans-serif; /* Consistent font for controls */
        }
        .preview-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }
        .controls-bar {
            display: flex;
            flex-wrap: wrap; /* Allow controls to wrap on smaller screens */
            justify-content: space-between; 
            align-items: center;
            width: 90vw;
            max-width: 1000px; 
            margin-bottom: 10px;
            padding: 8px;
            background-color: rgba(255, 255, 255, 0.85);
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .flipbook-nav, .tts-controls, .background-selector-form {
            margin: 5px; /* Spacing between control groups */
        }
        .flipbook-nav button, .back-to-edit-btn, .tts-controls button, .background-selector-form button {
            padding: 8px 15px; /* Uniform padding */
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            margin: 0 3px; /* Uniform margin */
        }
        .back-to-edit-btn { background-color: #007bff; color: white; }
        .back-to-edit-btn:hover { background-color: #0056b3; }
        .flipbook-nav button { background-color: #4CAF50; color: white; }
        .flipbook-nav button:hover { background-color: #45a049; }
        
        .tts-controls button { background-color: #ffc107; color: #333; }
        .tts-controls button:hover { background-color: #e0a800; }
        .tts-controls button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .tts-controls label, .background-selector-form label { margin-right: 5px; font-size: 0.9em; color: #333; }
        .tts-controls select, .tts-controls input[type="range"], .background-selector-form select {
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ccc;
            font-size: 0.9em;
            margin: 0 3px;
        }
        .background-selector-form button { background-color: #5bc0de; color: white; }
        .background-selector-form button:hover { background-color: #31b0d5; }
        #rateValue, #ttsStatus { font-size: 0.9em; margin-left: 5px; color: #555; }

        #flipbook {
            width: 90vw; max-width: 1000px; 
            height: 60vh; max-height: 700px; 
        }
        #flipbook .turn-page {
            background-color: white; border: 1px solid #999; overflow: hidden; 
        }
        #flipbook .page-content {
            padding: 20px; overflow-y: auto; height: 100%; box-sizing: border-box;
            font-family: 'Georgia', serif; font-size: 16px; line-height: 1.7;
        }
        #flipbook .page-content h1, #flipbook .page-content h2 {
            font-family: 'Arial', sans-serif; text-align: center; margin-bottom: 20px; color: #333;
        }
        #flipbook .page-content img.chapter-image {
            max-width: 60%; max-height: 250px; display: block; margin: 15px auto;
            border: 1px solid #ddd; padding: 3px; background-color: #f9f9f9;
        }
        #flipbook .cover-page .page-content {
            display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;
        }
        #flipbook .cover-page img.cover-image {
            max-width: 80%; max-height: 70%; object-fit: contain; 
        }
        #flipbook .cover-page h1.book-title { font-size: 2.5em; margin-top: 20px; }
    </style>
</head>
<body>
    {% include '_header.html' %} <!-- Include the global header -->
    <div class="preview-container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages" style="width: 90vw; max-width: 1000px;">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="controls-bar">
            <div class="flipbook-nav">
                <!-- "Back to Editor" link is now in the global header if needed, or can be a specific action -->
                <a href="{{ url_for('edit_book_page') }}" class="btn btn-secondary">Back to Editor</a>
                <button id="prevPage" class="btn btn-nav">Prev</button>
                <button id="nextPage" class="btn btn-nav">Next</button>
            </div>
            <div class="tts-controls">
                <button id="playTTS" title="Play" class="btn btn-tts"><img src="{{ url_for('static', filename='icons/play.svg') }}" alt="Play" style="width:16px;height:16px;vertical-align:middle;" onerror="this.style.display='none'; this.parentElement.innerHTML+='Play'"></button>
                <button id="pauseTTS" title="Pause" class="btn btn-tts"><img src="{{ url_for('static', filename='icons/pause.svg') }}" alt="Pause" style="width:16px;height:16px;vertical-align:middle;" onerror="this.style.display='none'; this.parentElement.innerHTML+='Pause'"></button>
                <button id="stopTTS" title="Stop" class="btn btn-tts"><img src="{{ url_for('static', filename='icons/stop.svg') }}" alt="Stop" style="width:16px;height:16px;vertical-align:middle;" onerror="this.style.display='none'; this.parentElement.innerHTML+='Stop'"></button>
                <select id="voiceSelect" title="Select Voice"></select>
                <label for="rateSlider" title="Speech Rate">Rate:</label>
                <input type="range" id="rateSlider" min="0.5" max="2" step="0.1" value="1">
                <span id="rateValue">1.0</span>
                <span id="ttsStatus" style="margin-left: 10px;"></span>
            </div>
            <div class="background-selector-form">
                <form method="POST" action="{{ url_for('set_book_background') }}" style="display: inline;">
                    <label for="background_choice">BG:</label>
                    <select name="background_choice" id="background_choice" title="Select Background">
                        {% for key, path in background_options.items() %}
                            <option value="{{ key }}" {% if path == book_data.background_pattern %}selected{% endif %}>
                                {{ key|capitalize }}
                            </option>
                        {% endfor %}
                    </select>
                    <button type="submit" title="Apply Background" class="btn btn-primary">Apply</button>
                </form>
            </div>
        </div>

        <div id="flipbook">
            <!-- Cover Page -->
            <div class="turn-page cover-page" data-page-number="0"> <!-- Special page number for cover -->
                <div class="page-content">
                    <h1 class="book-title">{{ book_data.book_title }}</h1>
                    {% if book_data.cover_image_url %}
                        <img src="{{ url_for('static', filename=book_data.cover_image_url) }}" alt="Cover Image" class="cover-image">
                    {% else %}
                        <p>(No Cover Image)</p>
                    {% endif %}
                </div>
            </div>

            <!-- Chapter Pages -->
            {% for chapter in book_data.chapters %}
                <div class="turn-page" data-page-number="{{ loop.index }}"> <!-- loop.index is 1-based -->
                    <div class="page-content">
                        <h2>{{ chapter.title }}</h2>
                        {% if chapter.image_url %}
                            <img src="{{ url_for('static', filename=chapter.image_url) }}" alt="Image for {{ chapter.title }}" class="chapter-image">
                        {% endif %}
                        <p>{{ chapter.content|safe }}</p> <!-- Assuming content is safe or sanitized if from LLM -->
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="{{ url_for('static', filename='lib/turnjs/turn.js') }}"></script>
    <script type="text/javascript">
        const currentBackgroundPath = {{ book_data.background_pattern|tojson }};
        const staticBaseUrl = "{{ url_for('static', filename='') }}"; 

        function applyBackground(patternPath) {
            if (patternPath && patternPath !== "None" && patternPath.trim() !== "") {
                document.body.style.backgroundImage = `url(${staticBaseUrl}${patternPath})`;
                document.body.style.backgroundRepeat = 'repeat';
                document.body.style.backgroundColor = ''; 
            } else {
                document.body.style.backgroundImage = 'none';
                document.body.style.backgroundColor = '#ccc'; 
            }
        }

        $(document).ready(function() {
            applyBackground(currentBackgroundPath);

            const flipbook = $("#flipbook");
            const bookWidth = flipbook.width(); 
            const bookHeight = flipbook.height(); 

            flipbook.turn({
                width: bookWidth,
                height: bookHeight,
                elevation: 50,
                gradients: true,
                autoCenter: true,
                when: {
                    turned: function(event, page, view) {
                        console.log("Turned to page: " + page + ", view: " + view.join(', '));
                        if (speechSynthesis.speaking || speechSynthesis.paused) {
                            stopTTS(); // Stop TTS when page turns
                        }
                        updateTTSButtonStates(false, false); // Reset button states (Play enabled)
                    }
                }
            });

            $("#prevPage").click(function(e){ e.preventDefault(); flipbook.turn("previous"); });
            $("#nextPage").click(function(e){ e.preventDefault(); flipbook.turn("next"); });

            // --- TTS ---
            const playBtn = document.getElementById('playTTS');
            const pauseBtn = document.getElementById('pauseTTS');
            const stopBtn = document.getElementById('stopTTS');
            const voiceSelect = document.getElementById('voiceSelect');
            const rateSlider = document.getElementById('rateSlider');
            const rateValueSpan = document.getElementById('rateValue');
            const ttsStatus = document.getElementById('ttsStatus');
            let currentUtterance = null;

            function updateTTSButtonStates(isSpeaking, isPaused) {
                playBtn.disabled = isSpeaking && !isPaused;
                pauseBtn.disabled = !isSpeaking || isPaused;
                stopBtn.disabled = !isSpeaking && !isPaused;
            }
            
            function populateVoiceList() {
                if (typeof speechSynthesis === 'undefined') return;
                const voices = speechSynthesis.getVoices();
                voiceSelect.innerHTML = '';
                voices.forEach(voice => {
                    const option = document.createElement('option');
                    option.textContent = `${voice.name} (${voice.lang})`;
                    option.setAttribute('data-lang', voice.lang);
                    option.setAttribute('data-name', voice.name);
                    voiceSelect.appendChild(option);
                });
            }

            function getCurrentPageText() {
                let textToRead = "";
                const currentView = flipbook.turn('view'); // Array of visible page numbers (e.g., [0,1] or [2,3])
                
                currentView.forEach(pageNumber => {
                    if (pageNumber === 0 && currentView.length > 1 && currentView[0] === 0 && currentView[1] === 1) { 
                        // Skip cover page if it's part of initial double view and not the only page shown
                        // This logic might need refinement based on how Turn.js handles single page vs double page for cover
                        // For now, if cover (page 0 by data-attr) is with page 1, we read page 1.
                        // If only cover is page 0, we read it.
                        if (flipbook.turn('page') === 1 && currentView.includes(1)) { // if showing page 1 (which is usually on right of cover)
                           // Get content of page 1 instead of cover
                           const pageElement = $(`#flipbook .turn-page[data-page-number="1"] .page-content`);
                            if (pageElement.length) {
                                textToRead += pageElement.text().trim() + "\n";
                            }
                            return; // skip the cover page 0 processing
                        }
                    }

                    const pageElement = $(`#flipbook .turn-page[data-page-number="${pageNumber}"] .page-content`);
                    if (pageElement.length) {
                        // Clone the element, remove any interactive elements like buttons if they existed
                        const contentClone = pageElement.clone();
                        // contentClone.find('button, select, input, form, a.back-to-edit-btn').remove(); // Example if there were such elements
                        textToRead += contentClone.text().trim() + "\n";
                    }
                });
                console.log("Text to read:", textToRead);
                return textToRead;
            }

            function playTTS() {
                if (!speechSynthesis) return;

                if (speechSynthesis.paused && currentUtterance) {
                    speechSynthesis.resume();
                } else {
                    if (speechSynthesis.speaking) speechSynthesis.cancel(); // Stop any previous
                    
                    const text = getCurrentPageText();
                    if (!text.trim()) {
                        ttsStatus.textContent = "No text on current page.";
                        return;
                    }
                    currentUtterance = new SpeechSynthesisUtterance(text);
                    const selectedVoiceName = voiceSelect.selectedOptions[0]?.getAttribute('data-name');
                    if (selectedVoiceName) {
                        const voices = speechSynthesis.getVoices();
                        currentUtterance.voice = voices.find(v => v.name === selectedVoiceName);
                    }
                    currentUtterance.rate = parseFloat(rateSlider.value);
                    
                    currentUtterance.onstart = () => {
                        ttsStatus.textContent = "Speaking...";
                        updateTTSButtonStates(true, false);
                    };
                    currentUtterance.onpause = () => {
                        ttsStatus.textContent = "Paused.";
                        updateTTSButtonStates(true, true);
                    };
                    currentUtterance.onresume = () => {
                        ttsStatus.textContent = "Resumed...";
                        updateTTSButtonStates(true, false);
                    };
                    currentUtterance.onend = () => {
                        ttsStatus.textContent = "Finished.";
                        updateTTSButtonStates(false, false);
                        currentUtterance = null;
                    };
                    currentUtterance.onerror = (event) => {
                        ttsStatus.textContent = "Error: " + event.error;
                        updateTTSButtonStates(false, false);
                        currentUtterance = null;
                    };
                    speechSynthesis.speak(currentUtterance);
                }
            }

            function pauseTTS() { if (speechSynthesis) speechSynthesis.pause(); }
            function stopTTS() { 
                if (speechSynthesis) {
                    speechSynthesis.cancel(); 
                    ttsStatus.textContent = "Stopped.";
                    updateTTSButtonStates(false, false);
                    currentUtterance = null;
                }
            }

            if (typeof speechSynthesis !== 'undefined') {
                populateVoiceList();
                if (speechSynthesis.onvoiceschanged !== undefined) {
                    speechSynthesis.onvoiceschanged = populateVoiceList;
                }
                playBtn.addEventListener('click', playTTS);
                pauseBtn.addEventListener('click', pauseTTS);
                stopBtn.addEventListener('click', stopTTS);
                rateSlider.addEventListener('input', () => {
                    rateValueSpan.textContent = parseFloat(rateSlider.value).toFixed(1);
                    if (currentUtterance && speechSynthesis.speaking && !speechSynthesis.paused) { 
                        // Some browsers might not support changing rate mid-speech.
                        // A common pattern is to stop and restart with new rate.
                        // For simplicity, this example applies it to new utterances.
                        currentUtterance.rate = parseFloat(rateSlider.value);
                    }
                });
                updateTTSButtonStates(false, false); // Initial state
            } else {
                ttsStatus.textContent = "TTS not supported by this browser.";
                [playBtn, pauseBtn, stopBtn, voiceSelect, rateSlider].forEach(el => el.disabled = true);
            }
        });
    </script>
</body>
</html>
