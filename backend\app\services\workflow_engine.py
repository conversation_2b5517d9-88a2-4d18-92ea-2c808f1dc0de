import json

# This dictionary stores the detailed instructions for each workflow.
# In a real application, this might come from a database or a configuration file.
WORKFLOW_PROMPTS = {
    "WF-1": {
        "title": "Text klarer/besser formulieren",
        "instructions": """
        Zweck: Eingegebenen Text stilistisch verbessern (klar, knapp, professionell), Sinn erhalten.
        Eingabe: TEXT.
        Vorgehen:
        1) Verständnis sichern; Ton „sachlich-hilfsbereit“.
        2) Problemstellen glätten (Redundanz, Schachtelsätze, unklare Verweise).
        3) Optionale Varianten (konservativ / aktiv / besonders knapp).
        Ausgabe – ERGEBNIS: Überarbeitete Fassung + (optional) 1–2 Alternativen.
        """
    },
    "WF-2": {
        "title": "Spenden/Zuwendungen aus Bild/PDF extrahieren und für Luzern beurteilen",
        "instructions": """
        Zweck: Aus dem extrahierten Text eine Liste der Zuwendungen erstellen und taxativ beurteilen (abzugsfähig vs. nicht abzugsfähig) inkl. Name/Empfänger, Betrag, Begründung gemäss Luzerner Praxis.
        Vorgehen:
        1) Extrahiere Positionen in eine Tabelle (Spalten mind.: Empfänger/Name, Betrag, Währung, Datum).
        2) Wende juristische Logik (Kanton Luzern) an: Spende vs. Gegenleistung; anerkannte gemeinnützige Institutionen; etc.
        3) Ordne jede Position zu: „Abzugsfähig“ oder „Nicht abzugsfähig“ mit Kurzbegründung.
        4) Bilde Summen je Kategorie + Gesamttotal.
        Ausgabe – ERGEBNIS:
        - TABELLE „ABZUGSFÄHIG“: Name/Empfänger | Betrag | Begründung (kurz)
        - TABELLE „NICHT ABZUGSFÄHIG“: Name/Empfänger | Betrag | Begründung (kurz)
        - TOTALISATION: Summe Abzugsfähig | Summe Nicht abzugsfähig | Gesamt
        """
    },
    # WF-3 and WF-4 would be defined here as well.
}

GENERAL_SYSTEM_PROMPT = """
Du bist ein KI-Assistent für Steuerfachpersonen im Kanton Luzern.
- Antworte standardmäßig auf Deutsch (Schweiz), sachlich, klar, präzise.
- Prüfe Eingaben auf Vollständigkeit. Wenn wesentliche Infos fehlen, stelle zuerst gezielte Rückfragen.
- Erkläre Annahmen explizit. Trenne Fakten, Interpretation und Empfehlung.
- Liefere immer die folgende Struktur als JSON-Objekt mit den Schlüsseln "kurzfazit", "ergebnis", "text_fuer_kunde", "interne_notizen", "rueckfragen".
- Rechts-Disclaimer: Du lieferst fachliche Vorschläge; endgültige Beurteilungen liegen bei der zuständigen Steuerbehörde. Beziehe dich auf Luzerner Steuerpraxis/Weisungen.
"""

def _build_final_prompt(workflow_id: str, user_input: str) -> str:
    """Constructs the full prompt for the LLM."""
    workflow_info = WORKFLOW_PROMPTS.get(workflow_id)
    if not workflow_info:
        raise ValueError(f"Workflow {workflow_id} not found.")

    final_prompt = f"""
{GENERAL_SYSTEM_PROMPT}

Du führst jetzt den folgenden Workflow aus:
Workflow ID: {workflow_id}
Workflow Titel: {workflow_info['title']}
Workflow Anleitung:
{workflow_info['instructions']}

Hier ist die Eingabe des Benutzers (z.B. aus einer Datei extrahierter Text):
---
{user_input}
---

Bitte generiere jetzt die Ausgabe als einzelnes JSON-Objekt, das alle geforderten Felder enthält.
"""
    return final_prompt

async def _call_llm_simulation(prompt: str, workflow_id: str) -> str:
    """
    *** SIMULATION of a Large Language Model (LLM) call. ***
    In a real application, this function would make an API call to a service like
    OpenAI, Anthropic, or Google Gemini.

    Example using OpenAI's library:
    --------------------------------
    from openai import OpenAI
    client = OpenAI(api_key="YOUR_API_KEY")
    response = client.chat.completions.create(
        model="gpt-4-turbo",
        messages=[
            {"role": "system", "content": "You are a helpful assistant outputting JSON."},
            {"role": "user", "content": prompt}
        ],
        response_format={"type": "json_object"}
    )
    return response.choices[0].message.content
    """
    print("--- SIMULATED LLM CALL ---")
    print(f"--- PROMPT FOR {workflow_id} ---")
    # print(prompt) # This would be too verbose for the agent's output

    # Return a hardcoded, structured response based on the workflow for demonstration.
    if workflow_id == "WF-2":
        simulated_response = {
            "kurzfazit": "Von den total CHF 650.00 an Zuwendungen sind CHF 600.00 als Spenden abzugsfähig. Die restlichen CHF 50.00 sind nicht abzugsfähig, da es sich um einen Mitgliederbeitrag mit Gegenleistung handelt.",
            "ergebnis": {
                "abzugsfähig": [
                    {"empfaenger": "Rotes Kreuz Schweiz", "betrag": "500.00 CHF", "begruendung": "Anerkannte gemeinnützige Organisation (ZEWO-zertifiziert)."},
                    {"empfaenger": "Stiftung Kinderhilfe", "betrag": "100.00 CHF", "begruendung": "Anerkannte gemeinnützige Organisation."}
                ],
                "nicht_abzugsfähig": [
                    {"empfaenger": "Fussballclub Lokal", "betrag": "50.00 CHF", "begruendung": "Mitgliederbeitrag mit Gegenleistung (z.B. Gratiseintritt)."}
                ],
                "totalisation": {
                    "summe_abzugsfähig": "600.00 CHF",
                    "summe_nicht_abzugsfähig": "50.00 CHF",
                    "gesamt": "650.00 CHF"
                }
            },
            "text_fuer_kunde": "Sehr geehrte/r Kund/in,\n\nwir haben die von Ihnen eingereichten Belege geprüft. Von den gesamten Zuwendungen in Höhe von CHF 650.00 können wir CHF 600.00 in Ihrer Steuererklärung als abzugsfähige Spenden deklarieren. Der Beitrag an den Fussballclub über CHF 50.00 ist nicht abzugsfähig, da er als Mitgliederbeitrag mit einer direkten Gegenleistung verbunden ist.\n\nFreundliche Grüsse,\nIhr Treuhandbüro",
            "interne_notizen": "Klassifizierung gemäss LU-Steuerpraxis. Rotes Kreuz und Kinderhilfe sind unbestritten. Beitrag FC ist klar ein nicht abzugsfähiger Mitgliederbeitrag.",
            "rueckfragen": []
        }
        return json.dumps(simulated_response, indent=2, ensure_ascii=False)
    else:
        # Default response for other workflows like WF-1
        simulated_response = {
            "kurzfazit": "Der Text wurde erfolgreich überarbeitet und klarer formuliert.",
            "ergebnis": {
                "ueberarbeitete_fassung": "Dies ist die verbesserte Version des Textes. Sie ist nun klarer, prägnanter und professioneller.",
                "alternativen": [
                    "Alternative 1: Eine etwas konservativere Formulierung.",
                    "Alternative 2: Eine besonders knappe Fassung."
                ]
            },
            "text_fuer_kunde": "Hier ist der überarbeitete Text, den Sie direkt verwenden können:\n\n'Dies ist die verbesserte Version des Textes. Sie ist nun klarer, prägnanter und professioneller.'",
            "interne_notizen": "Originaltext war umständlich formuliert mit Schachtelsätzen. Wurde geglättet.",
            "rueckfragen": []
        }
        return json.dumps(simulated_response, indent=2, ensure_ascii=False)


async def run_workflow(workflow_id: str, user_input: str) -> str:
    """
    Runs the specified workflow by building a prompt and calling the LLM.
    """
    if workflow_id not in WORKFLOW_PROMPTS:
        raise ValueError(f"Unbekannter Workflow: {workflow_id}")

    # 1. Build the detailed prompt for the AI
    final_prompt = _build_final_prompt(workflow_id, user_input)

    # 2. Call the (simulated) LLM to get the structured response
    llm_response_json_str = await _call_llm_simulation(final_prompt, workflow_id)

    return llm_response_json_str
