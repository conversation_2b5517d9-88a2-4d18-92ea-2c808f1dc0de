<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Book Creator</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    {% include '_header.html' %}
    <div class="page-wrapper"> <!-- Added page-wrapper -->
        <div class="container">
            <h1 class="page-title">Start Your New Book</h1>
            <form id="bookCreationForm" method="POST" action="{{ url_for('generate_book_and_redirect') }}">
            <div class="form-group">
                <label for="genre">Genre:</label>
                <select id="genre" name="genre">
                    <option value="fantasy">Fantasy</option>
                    <option value="sci-fi">Sci-Fi</option>
                    <option value="romance">Romance</option>
                    <option value="thriller">Thriller</option>
                    <option value="non-fiction">Non-Fiction</option>
                    <option value="custom">Other (Please specify)</option>
                </select>
                <input type="text" id="genre_custom" name="genre_custom" placeholder="Specify genre if 'Other'" style="display:none; margin-top: 5px;">
            </div>

            <div class="form-group">
                <label for="target_audience">Target Audience:</label>
                <input type="text" id="target_audience" name="target_audience" placeholder="e.g., Children, Young Adults, Professionals">
            </div>

            <div class="form-group">
                <label for="content_focus">Content Focus Points (Keywords or Brief Description):</label>
                <textarea id="content_focus" name="content_focus" rows="4" placeholder="e.g., Space exploration, character development, historical accuracy"></textarea>
            </div>

            <div class="form-group">
                <label for="writing_style">Writing Style:</label>
                <select id="writing_style" name="writing_style">
                    <option value="descriptive">Descriptive</option>
                    <option value="concise">Concise</option>
                    <option value="academic">Academic</option>
                    <option value="humorous">Humorous</option>
                    <option value="narrative">Narrative</option>
                    <option value="custom">Other (Please specify)</option>
                </select>
                <input type="text" id="writing_style_custom" name="writing_style_custom" placeholder="Specify style if 'Other'" style="display:none; margin-top: 5px;">
            </div>

            <div class="form-group">
                <label for="num_chapters">Number of Chapters:</label>
                <input type="number" id="num_chapters" name="num_chapters" min="1" value="10">
            </div>

            <fieldset class="form-group">
                <legend>Optional: Chapter Titles</legend>
                <div id="chapterTitlesContainer">
                    <!-- Example of a chapter title input -->
                    <div class="chapter-title-entry">
                        <label for="chapter_title_1">Chapter 1 Title:</label>
                        <input type="text" id="chapter_title_1" name="chapter_title_1" placeholder="Enter title for Chapter 1">
                    </div>
                    <!-- More chapter title inputs can be added here or dynamically with JavaScript -->
                </div>
                <button type="button" id="addChapterTitleButton" style="margin-top: 10px;">Add Another Chapter Title</button>
            </fieldset>
            
            <div class="form-group text-center"> <!-- Added text-center for button alignment -->
                <input type="submit" value="Start Creating Book" class="btn btn-primary btn-large">
            </div>
        </form>
        </div> <!-- Close container -->
    </div> <!-- Close page-wrapper -->
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
