import json
from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Body
from starlette.responses import Response
from pydantic import BaseModel
from typing import Optional

from fastapi.middleware.cors import CORSMiddleware
from app import services

app = FastAPI(title="Steuer-Assistent API")

# --- Pydantic Models ---
class WorkflowTextRequest(BaseModel):
    workflow_id: str
    text_input: str

# --- CORS Configuration ---
origins = [
    "http://localhost:3000",
    "localhost:3000",
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- API Endpoints ---
@app.get("/")
def read_root():
    return {"message": "Willkommen beim Steuer-Assistent API"}

@app.post("/execute-workflow/text")
async def execute_workflow_with_text(request: WorkflowTextRequest):
    """
    Executes a workflow based on direct text input.
    """
    try:
        result_json_str = await services.workflow_engine.run_workflow(
            workflow_id=request.workflow_id,
            user_input=request.text_input
        )
        return Response(content=result_json_str, media_type="application/json")
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@app.post("/execute-workflow/file")
async def execute_workflow_with_file(workflow_id: str = Body(...), file: UploadFile = File(...)):
    """
    Executes a workflow based on an uploaded file (PDF or image).
    """
    try:
        extracted_text = await services.file_processor.process_file(file)
        result_json_str = await services.workflow_engine.run_workflow(
            workflow_id=workflow_id,
            user_input=extracted_text
        )
        return Response(content=result_json_str, media_type="application/json")
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")
