body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f4f7f9;
    color: #333;
    margin: 0;
    padding: 20px;
}

#root {
    max-width: 900px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    padding: 2rem;
}

h1 {
    color: #1a3a53;
    text-align: center;
    margin-bottom: 2rem;
}

.app-container {
    display: flex;
    gap: 2rem;
}

.form-container {
    flex: 1;
}

.result-container {
    flex: 1.5;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    min-height: 400px;
    background-color: #fdfdfd;
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

select, textarea, input[type="file"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
}

textarea {
    min-height: 150px;
    resize: vertical;
}

button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    width: 100%;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #0056b3;
}

button:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
}

.result-section {
    margin-bottom: 1.5rem;
}

.result-section h3 {
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    color: #1a3a53;
}

.result-section pre {
    background-color: #eee;
    padding: 1rem;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.result-section table {
    width: 100%;
    border-collapse: collapse;
}

.result-section th, .result-section td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.result-section th {
    background-color: #f2f2f2;
}

.error-message {
    color: #d9534f;
    background-color: #f2dede;
    border: 1px solid #ebccd1;
    padding: 1rem;
    border-radius: 4px;
}

.loading-message {
    color: #5bc0de;
}
