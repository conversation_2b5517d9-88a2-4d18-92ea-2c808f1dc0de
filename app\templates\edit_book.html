<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Your Book - {{ book_data.book_title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    {% include '_header.html' %}
    <div class="page-wrapper"> <!-- Added page-wrapper -->
        <div class="container editor-container">
            <!-- Flash messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="editor-header">
            <a href="{{ url_for('index') }}" class="back-link">&larr; Back to Form</a>
            <h1>Editing: {{ book_data.book_title }}</h1>
        </div>

        <!-- Cover Image Section -->
        <div class="cover-image-section form-group image-management-area">
            <h3>Book Cover Image</h3>
            {% if book_data.cover_image_url %}
                <div class="image-preview">
                <img src="{{ url_for('static', filename=book_data.cover_image_url) }}" alt="Current Cover Image" class="img-preview">
                </div>
            {% else %}
                <p>No cover image set.</p>
            {% endif %}

            <div class="image-actions">
                <div class="upload-form">
                    <h4>Upload Cover Image:</h4>
                    <form method="POST" action="{{ url_for('upload_cover_image') }}" enctype="multipart/form-data">
                        <input type="file" name="image" class="form-control-file" required>
                        <button type="submit" class="btn btn-secondary" style="margin-top:10px;">Upload Cover</button>
                    </form>
                </div>
                <div class="ai-generation-form">
                    <h4>Or, Generate AI Cover Image (Simulated):</h4>
                    <form method="POST" action="{{ url_for('generate_ai_cover_image') }}">
                        <div class="form-group">
                            <label for="cover_image_style">Style:</label>
                            <input type="text" id="cover_image_style" name="image_style" class="form-control" placeholder="e.g., cartoon, realistic, fantasy" value="fantasy">
                        </div>
                        <div class="form-group">
                            <label for="cover_image_prompt">Prompt:</label>
                            <input type="text" id="cover_image_prompt" name="image_prompt" class="form-control" placeholder="Describe the cover image" value="A dragon flying over a castle.">
                        </div>
                        <button type="submit" class="btn btn-ai-generate" style="margin-top:10px;">Generate AI Cover</button>
                    </form>
                </div>
            </div>
        </div>
        <hr>

        {% if book_data.chapters and book_data.chapters|length > 0 %}
            {% for chapter in book_data.chapters %}
                <div class="chapter-editor">
                    <h2>
                        <input type="text" name="chapter_title_{{ loop.index0 }}" value="{{ chapter.title }}" class="editable-title chapter-title-input">
                    </h2>
                    
                    <!-- Chapter Image Section -->
                    <div class="chapter-image-section form-group image-management-area">
                        <h4>Chapter {{ loop.index }} Image</h4>
                        {% if chapter.image_url %}
                            <div class="image-preview">
                            <img src="{{ url_for('static', filename=chapter.image_url) }}" alt="Current Image for Chapter {{ loop.index }}" class="img-preview">
                            </div>
                        {% else %}
                            <p>No image set for this chapter.</p>
                        {% endif %}

                        {% if loop.index0 == 0 %} <!-- Only allowing image operations for first chapter as per simplified requirement -->
                        <div class="image-actions">
                            <div class="upload-form">
                                <h4>Upload Chapter Image:</h4>
                                <form method="POST" action="{{ url_for('upload_chapter_image', chapter_index=loop.index0) }}" enctype="multipart/form-data">
                                    <input type="file" name="image" class="form-control-file" required>
                                    <button type="submit" class="btn btn-secondary" style="margin-top:10px;">Upload Chapter Image</button>
                                </form>
                            </div>
                            <div class="ai-generation-form">
                                <h4>Or, Generate AI Chapter Image (Simulated):</h4>
                                <form method="POST" action="{{ url_for('generate_ai_chapter_image', chapter_index=loop.index0) }}">
                                    <div class="form-group">
                                        <label for="chapter_image_style_{{loop.index0}}">Style:</label>
                                        <input type="text" id="chapter_image_style_{{loop.index0}}" name="image_style" class="form-control" placeholder="e.g., cartoon, realistic" value="realistic">
                                    </div>
                                    <div class="form-group">
                                        <label for="chapter_image_prompt_{{loop.index0}}">Prompt:</label>
                                        <input type="text" id="chapter_image_prompt_{{loop.index0}}" name="image_prompt" class="form-control" placeholder="Describe the chapter image" value="A mysterious forest path.">
                                    </div>
                                    <button type="submit" class="btn btn-ai-generate" style="margin-top:10px;">Generate AI Chapter Image</button>
                                </form>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    <hr style="margin: 15px 0;">

                    <textarea name="chapter_content_{{ loop.index0 }}" class="editable-content chapter-content-textarea" rows="20">{{ chapter.content }}</textarea>
                </div>
            {% endfor %}
        {% else %}
            <p>No chapters generated.</p>
        {% endif %}

        <div class="form-group editor-actions">
        <div class="form-group editor-actions">
            <a href="{{ url_for('book_preview_page') }}" class="btn btn-info" style="margin-right: 10px;">Book Preview</a>
            <a href="{{ url_for('export_pdf') }}" class="btn btn-success" style="margin-right: 10px;">Download PDF</a>
            <a href="{{ url_for('export_epub') }}" class="btn btn-warning" style="margin-right: 10px;">Download EPUB</a>
            <button type="button" id="saveChangesButton" class="btn btn-primary">Save Changes (Not Implemented)</button>
        </div>

        <div class="kdp-guidance form-group">
            <h4>Amazon KDP Export Guidance</h4>
            <p>When publishing on Amazon Kindle Direct Publishing (KDP), use the generated files as follows:</p>
            
            <h5>For KDP E-books (Kindle):</h5>
            <ul>
                <li>The generated <strong>EPUB file</strong> is the recommended format for uploading to KDP for e-books.</li>
                <li>It's highly recommended to validate your EPUB file using Amazon's Kindle Previewer tool before publishing. This helps ensure compatibility and the best reading experience on Kindle devices.</li>
                <li>During their process, KDP will typically convert your uploaded EPUB file into their proprietary Kindle Package Format (KPF).</li>
            </ul>

            <h5>For KDP Print Books (Paperback/Hardcover):</h5>
            <ul>
                <li>The generated <strong>PDF file</strong> can serve as a starting point for your KDP print book's interior.</li>
                <li><strong>Important:</strong> You <strong>must</strong> configure your book's trim size, margins, bleed settings, and other layout options directly within the KDP platform when setting up your print manuscript.</li>
                <li>The PDF generated by this tool currently uses a standard A4 page size by default. This may not match your desired print book size. Future updates to this tool may offer more PDF customization options. For now, ensure the KDP setup matches your intended book format, and be prepared to adjust if needed.</li>
                <li>For the cover of your print book, KDP provides a separate cover calculator and uploader where you'll typically upload a print-ready PDF cover designed to specific dimensions (including spine width).</li>
            </ul>
            <p>Always refer to the latest KDP guidelines on Amazon's website for the most up-to-date publishing requirements.</p>
        </div>

        <div class="export-settings-section form-group">
            <h4>Export Customization Settings</h4>
            <form method="POST" action="{{ url_for('save_export_settings') }}">
                <fieldset>
                    <legend>PDF Settings</legend>
                    <div class="form-group">
                        <label for="pdf_page_size">Page Size:</label>
                        <select name="pdf_page_size" id="pdf_page_size" class="form-control">
                            {% for value, display_name in PDF_PAGE_SIZE_OPTIONS.items() %}
                                <option value="{{ value }}" {% if export_settings.pdf_page_size == value %}selected{% endif %}>{{ display_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-3">
                            <label for="pdf_margin_top">Top Margin (mm):</label>
                            <input type="number" name="pdf_margin_top" id="pdf_margin_top" value="{{ export_settings.pdf_margin_top }}" class="form-control" step="0.1" min="0">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="pdf_margin_bottom">Bottom Margin (mm):</label>
                            <input type="number" name="pdf_margin_bottom" id="pdf_margin_bottom" value="{{ export_settings.pdf_margin_bottom }}" class="form-control" step="0.1" min="0">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="pdf_margin_left">Left Margin (mm):</label>
                            <input type="number" name="pdf_margin_left" id="pdf_margin_left" value="{{ export_settings.pdf_margin_left }}" class="form-control" step="0.1" min="0">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="pdf_margin_right">Right Margin (mm):</label>
                            <input type="number" name="pdf_margin_right" id="pdf_margin_right" value="{{ export_settings.pdf_margin_right }}" class="form-control" step="0.1" min="0">
                        </div>
                    </div>
                </fieldset>
                <br>
                <fieldset>
                    <legend>Font Settings (Applied to PDF & EPUB Body)</legend>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <label for="font_family">Font Family:</label>
                            <select name="font_family" id="font_family" class="form-control">
                                {% for value, display_name in FONT_FAMILY_OPTIONS.items() %}
                                    <option value="{{ value }}" {% if export_settings.font_family == value %}selected{% endif %}>{{ display_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="font_size">Base Font Size (pt):</label>
                            <input type="number" name="font_size" id="font_size" value="{{ export_settings.font_size }}" class="form-control" step="1" min="6">
                        </div>
                    </div>
                </fieldset>
                <br>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Save Export Settings</button>
                </div>
            </form>
        </div>
        
        <div class="debug-info">
            <h3>Prompt Sent to LLM (Simulated):</h3>
            <pre>{{ book_data.prompt_used }}</pre>
        </div>
    </div>
    <script>
        // Basic JS to make alert messages dismissible (optional)
        document.addEventListener('DOMContentLoaded', (event) => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.addEventListener('click', function() {
                    this.style.display = 'none';
                });
            });
        });
    </script>
</body>
</html>
