/* Basic EPUB Styling */
body {
    font-family: serif;
    line-height: 1.6;
    margin: 1em;
}

h1 { /* Chapter Titles */
    font-size: 1.8em;
    font-weight: bold;
    text-align: center;
    margin-top: 1em;
    margin-bottom: 1em;
    page-break-before: always; /* Start each chapter on a new page */
}

h1.book-main-title { /* For the main book title if used on a title page */
    font-size: 2.5em;
    text-align: center;
    margin-top: 30%;
    page-break-before: avoid;
    page-break-after: always;
}

p {
    margin-bottom: 0.8em;
    text-align: justify;
}

img.cover-image {
    display: block;
    max-width: 90%;
    max-height: 80vh; /* Viewport height based */
    margin: 20px auto;
    text-align: center; /* For block element */
}

img.chapter-image {
    display: block;
    max-width: 70%;
    max-height: 40vh; /* Viewport height based */
    margin: 15px auto; /* Centered */
}

/* Basic navigation styling if needed, though NCX handles TOC */
.epub-toc-title {
    font-size: 1.5em;
    font-weight: bold;
    margin-bottom: 1em;
}

ol.epub-toc {
    list-style-type: none;
    padding-left: 0;
}

ol.epub-toc li a {
    text-decoration: none;
    color: #333;
}

ol.epub-toc li a:hover {
    color: #000;
    text-decoration: underline;
}
