@page {
    size: A4;
    margin: 2cm;
}

body {
    font-family: 'Times New Roman', Times, serif;
    font-size: 12pt;
    line-height: 1.5;
    color: #333;
}

.pdf-page {
    page-break-before: always;
    /* background-color: white;  Will be overridden by body if background pattern is used */
}

.cover-page {
    page-break-before: avoid; /* Cover is the first page */
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 25cm; /* Approx A4 height minus margins */
}

.cover-image {
    max-width: 80%;
    max-height: 60%;
    object-fit: contain;
    margin-bottom: 30px;
}

.book-title {
    font-size: 32pt;
    font-weight: bold;
    margin-top: 20px;
    color: #000;
}

.chapter-start-page {
    /* Styles for the page where a chapter begins */
}

.chapter-title {
    font-size: 24pt;
    font-weight: bold;
    text-align: center;
    margin-bottom: 25px;
    margin-top: 10px; /* Space after potential page break */
    color: #000;
    page-break-after: avoid; /* Keep title with content */
}

.chapter-image {
    max-width: 70%;
    max-height: 300px; /* Adjust as needed */
    display: block;
    margin: 20px auto; /* Centered */
    page-break-inside: avoid;
}

.chapter-content {
    text-align: justify;
    orphans: 3;
    widows: 3;
}

.chapter-content p {
    margin-bottom: 0.5em; /* Spacing between paragraphs if using <p> tags, not just <br> */
}

/* If a background pattern is applied to body */
body.with-background {
    /* background-size: cover; Or 'auto' or '100% 100%' depending on desired effect */
    /* background-repeat: no-repeat; Or 'repeat' if it's a texture */
}
